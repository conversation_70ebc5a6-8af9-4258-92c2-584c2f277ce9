<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <style>
        @media (max-width: 560px) {
            body {
                font-size: 16px !important;
            }
        }

        a {
            color: blue;
            text-decoration: none !important;
        }
    </style>
</head>

@php
use App\Setting;
use Carbon\Carbon;

//format date if needed
$start_date = Setting::formateDate($company, Carbon::parse($start_date));
$end_date = Setting::formateDate($company, Carbon::parse($end_date));
@endphp

<body style="font-family: -apple-system, 'Segoe UI', sans-serif;font-size: 18px;background-color: #f5f5f5;padding: 2rem 1rem;margin: 0;">
    <div style="width: 100%; max-width: 560px; margin: 0 auto;background-color: white; border-radius: 0.4rem;box-shadow: 0 0 1rem rgba(0,0,0,.12);overflow: hidden;">
        <div style="width: 90%;margin: 0 auto;padding:1rem 0;">
            {!! __('strings.mail_schedule_edit_title',['start_date'=>$start_date,'end_date'=>$end_date]) !!}
            <p>{!! __('strings.mail_schedule_repeat_week',['repeat_week'=>$repeat_week]) !!}</p>
            <p>{{ __('strings.mail_schedule_affect_day') }}</p>

            <b>
                @foreach ($days as $index => $day)
                @if ($index == 0)
                {{ $day }}
                @else
                ,{{ $day }}
                @endif
                @endforeach
            </b>
            <p>{{ __('strings.mail_schedule_working_hours') }}</p>
            <b>
                @foreach ($working_hours as $working_hour)
                <p>{{ Setting::formateTime($company, Carbon::parse($working_hour['start_time'])) }} -
                    {{ Setting::formateTime($company, Carbon::parse($working_hour['end_time'])) }}
                </p>
                @endforeach
            </b>

            <p style="margin-bottom: 0.2rem;margin-top: 2rem;">{{ __('strings.best_regards') }}</p>
            <p style="margin-top: 0;margin-bottom: 0.5rem;"><b>{{ __('strings.Team_MERIDIQ') }}</b></p>
        </div>
    </div>
</body>

</html>