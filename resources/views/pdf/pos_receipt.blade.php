<!doctype html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    @if ($receipt_type == 'copy')
        <title>{{ __('pos_strings.pdf.title_receipt_copy') }} - {{ $receipt_no }}</title>
    @else
        <title>{{ $control_code || $is_online ? __('pos_strings.pdf.receipt') : __('pos_strings.pdf.cash_invoice')}}  - {{ $receipt_no }}</title>
    @endif

    <link rel="stylesheet" href="{{ storage_path('css/dist/pos_report.css') }}" type="text/css">
</head>

<body>
    <header class="clearfix py-6 px-8 m-auto h-12 bg-purple text-white">
        <table class="float-left">
            <tr>
                <td>
                    <div class="h-12 w-12 rounded-full overflow-hidden relative">
                        <img class="u-object-fit--fallback h-12" src="{{ $from_image ? $from_image : 'images/meridiq_logo.png' }}">
                    </div>
                </td>
                <td>
                    <h2 class="text-xl mx-4 font-semibold leading-none">{{ $from ?? config('app.name') }}</h2>
                </td>
            </tr>
        </table>
        <div class="float-right text-left">
            @if ($receipt_type == 'copy')
                <h2 class="text-xl font-semibold leading-none">{{ __('pos_strings.pdf.title_receipt_copy') }}</h2>
            @else
                <h2 class="text-xl font-semibold leading-none">{{ $control_code || $is_online ? __('pos_strings.pdf.receipt') : __('pos_strings.pdf.cash_invoice')}}</h2>
            @endif
            <p class="text-sm leading-none text-right">{{ __('pos_strings.pdf.org_no') }}. {{ $receipt_org_no }}</p>
        </div>
    </header>
    <footer class="leading-none">{{ __('pos_strings.pdf.powered_by') }} <a href="https://meridiq.com">{{ config('app.name') }}</a></footer>

    <main>
        @if(isset($message) && $message)
        <h1 class="text-3xl font-light text-center mt-6">{{ $message }}</h1>
        @endif

        <div class="mx-6 mt-6">
        <table border="0" cellspacing="0" cellpadding="0" class="w-full border-none border-collapse border-spacing-0 text-base leading-4">
            <tbody>
                <tr class="">
                    <td class="pb-2 leading-none text-grayDark text-left">{{ __('pos_strings.pdf.from') }}</td>
                    <td class="pb-2 leading-none text-grayDark text-left">{{ __('pos_strings.pdf.billed_to') }}</td>
                    <td class="pb-2 leading-none text-grayDark text-right">{{ __('pos_strings.pdf.receipt_no') }}</td>
                </tr>
                <tr class="">
                    <td class="leading-none text-left">{{ $from }}</td>
                    <td class="leading-none text-left">{{ $to }}</td>
                    <td class="leading-none text-right">#{{ $receipt_no }}</td>
                </tr>
                <tr class="">
                    <td class="leading-none text-grayMedium text-sm text-left"><a href="mailto:{{ $from_email }}">{{ $from_email }}</a></td>
                    <td class="leading-none text-grayMedium text-sm text-left"><a href="mailto:{{ $to_email }}">{{ $to_email }}</a></td>
                    <td class="leading-none text-grayMedium text-sm text-right">{{ $created_at }}</td>
                </tr>
                <tr class="">
                    <td class="leading-none text-grayMedium text-sm text-left">{{ $from_phone }}</td>
                    <td class="leading-none text-grayMedium text-sm text-left">{{ $to_phone }}</td>
                    <td class="leading-none text-grayMedium text-sm text-right"></td>
                </tr>
                <tr class="">
                    <td class="leading-none text-grayMedium text-sm text-left">{{ __('pos_strings.pdf.org_no') }}. {{ $receipt_org_no }}</td>
                    <td class="leading-none text-grayMedium text-sm text-left"></td>
                    <td class="leading-none text-grayMedium text-sm text-right"></td>
                </tr>
                <tr class="">
                    <td class="leading-none text-grayMedium text-sm text-left"><p style="max-width: 5cm;">{{ $from_address }}</p></td>
                    <td class="">
                        <p class="leading-none text-left">{{ __('pos_strings.pdf.payment_type') }}</p>
                        <p class="leading-none text-green-600 text-sm text-left">{{ $payment_type }}</p>
                    </td>
                    <td class="leading-none text-grayMedium text-sm text-right"></td>
                </tr>
            </tbody>
        </table>
        </div>

        <div class="mt-6">
        <table border="0" cellspacing="0" cellpadding="0" class="w-full border-collapse border-spacing-0 text-base leading-4">
            <thead class="p-0 border-y border-purpleo18">
                <tr class="">
                    <th class="px-6 py-3 font-medium text-grayMedium text-left" style="min-width: 100px;">{{ __('pos_strings.pdf.order') }}</th>
                    <th class="px-3 py-3 font-medium text-grayMedium text-left" style="min-width: 50px;">{{ __('pos_strings.pdf.price') }}</th>
                    <th class="px-3 py-3 font-medium text-grayMedium text-center" style="min-width: 50px;">{{ __('pos_strings.pdf.unit') }}</th>
                    <th class="px-3 font-medium text-grayMedium text-right">
                        <div class="">
                            {{ __('pos_strings.pdf.vat') }}
                        </div>
                        <div class="">
                            incl.
                        </div>
                    </th>
                    <th class="px-6 py-3 font-medium text-grayMedium text-right">{{ __('pos_strings.pdf.sub_total') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($products as $key => $product)
                    <tr class="p-0 border-y border-purpleo18">
                        <td class="px-6 py-3 text-left">
                            <p class="font-medium leading-none">{{ $product['name'] }}</p>
                            <p class="text-grayMedium text-sm font-medium leading-none">({{ __('pos_strings.pdf.id') }} {{ $product['id'] }})</p>
                        </td>
                        <td class="px-3 py-3 text-left">{{ withCurrency($product['price']) }}</td>
                        <td class="px-3 py-3 text-center">{{ $product['unit'] }}</td>
                        <td class="px-3 py-3 text-right">{{ $product['vat'] }}%</td>
                        <td class="px-6 py-3 text-right text-purple font-medium">{{ withCurrency($product['price'] * $product['unit'])}}</td>
                    </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="3" class="align-top pt-3" style="max-width: 300px;">
                        <table class="w-full border-none border-collapse border-spacing-0 text-base leading-none">
                            <tbody>
                                <tr>
                                    <td class="px-6 py-2 text-left font-normal leading-none text-grayDark">
                                        {{ __('pos_strings.pdf.payment') }}
                                    </td>
                                </tr>
                                @if($gift_card['id'])
                                <tr>
                                    <td class="px-6 py-1 text-left font-normal leading-none text-grayDark">
                                        <p class="font-normal leading-none text-grayDim"> {{ __('pos_strings.gift_card.gift_card') }}: {{ withCurrency($gift_card['amount']) }}</p>
                                        <p class="font-medium leading-none text-grayDark"> {{ __('pos_strings.pdf.gift_card_no') }}. <span class="text-grayDim leading-none">{{ $gift_card['id'] }}</span></p>
                                    </td>
                                </tr>
                                @endif

                                @if($payment_card && $paid_amount)
                                <tr>
                                    <td class="px-6 py-1 text-left font-normal leading-none text-grayDim">
                                        <p class="font-normal leading-none text-grayDim"> {{ __('pos_strings.pdf.credit_debit_card') }}: {{ withCurrency($paid_amount) }}</p>
                                        <p class="font-medium leading-none text-grayDark"><span class="text-grayDim leading-none">{{$payment_card}}</span></p>
                                    </td>
                                </tr>
                                @endif

                                @if($payment_method == App\CompanyReceipt::PAYMENT_METHOD_SWISH && $paid_amount)
                                <tr>
                                    <td class="px-6 py-1 text-left font-normal leading-none text-grayDim">
                                        <p class="font-normal leading-none text-grayDim"> {{ __('pos_strings.swish.swish') }}: {{ withCurrency($paid_amount) }}</p>
                                    </td>
                                </tr>
                                @endif

                                <tr>
                                    <td class="px-6 py-1 text-left font-normal leading-none text-grayDark">
                                        <p class="font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.merchant_id') }}: <span class="text-grayDim leading-none">{{ $merchant_id }}</span></p>
                                        @if($terminal_id)<p class="font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.terminal_id') }}: <span class="text-grayDim leading-none">{{ $terminal_id }}</span></p>@endif
                                        @if($ecr_id)<p class="font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.ecr_id') }}: <span class="text-grayDim leading-none">{{ $ecr_id }}</span></p>@endif
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                    <td colspan="2" class="align-top pt-3">
                        <table border="0" cellspacing="0" cellpadding="0" class="w-full border-none border-collapse border-spacing-0 text-base leading-none">
                            <tbody>
                                <tr>
                                    <td class=" px-6 py-2 text-left font-normal leading-none text-grayDark">
                                        {{ __('pos_strings.pdf.summary_sub_total') }}
                                    </td>
                                    <td class="px-6 py-2 text-right font-medium text-grayDim leading-none">
                                        {{ withCurrency($sub_total) }}
                                    </td>
                                </tr>
                                @if($discount)
                                <tr>
                                    <td class="px-6 py-2 text-left font-normal leading-none text-grayDark">
                                        {{ __('pos_strings.pdf.discount') }}
                                    </td>
                                    <td class="px-6 py-2 text-right font-medium text-purple leading-none">
                                        -{{ withCurrency($discount) }}
                                    </td>
                                </tr>
                                @endif
                                <tr class="text-white">
                                    <td colspan="2">
                                        <div class="bg-purple rounded-l-lg px-6 py-4 mt-2">
                                            <table class="w-full">
                                                <tr>
                                                    <td class="text-left font-semibold leading-none text-lg">{{ __('pos_strings.pdf.total') }}</td>
                                                    <td class="text-right font-semibold leading-none text-lg">{{ withCurrency($total) }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="">
                                    <td colspan="2">
                                        <div class="px-6 py-2">
                                            <table class="w-full">
                                                <thead class="">
                                                    <tr class="">
                                                        <th class="py-2 font-medium text-grayMedium text-left">{{ __('pos_strings.pdf.tax') }}%</th>
                                                        <th class="py-2 font-medium text-grayMedium text-left">{{ __('pos_strings.pdf.tax') }}</th>
                                                        <th class="py-2 font-medium text-grayMedium text-left">{{ __('pos_strings.pdf.net') }}</th>
                                                        <th class="py-2 font-medium text-grayMedium text-left" >{{ __('pos_strings.pdf.gross') }}</th>
                                                    </tr>
                                                </thead>
                                                @foreach ($vat_info as $value)
                                                <tr>
                                                    <td class="text-left font-normal leading-none text-md">{{ withCurrency($value['percentage'], null, \NumberFormatter::DECIMAL_SEPARATOR_SYMBOL) }}</td>
                                                    <td class="text-left font-normal leading-none text-md">{{ withCurrency($value['tax'], null, \NumberFormatter::DECIMAL_SEPARATOR_SYMBOL) }}</td>
                                                    <td class="text-left font-normal leading-none text-md">{{ withCurrency($value['net'], null, \NumberFormatter::DECIMAL_SEPARATOR_SYMBOL) }}</td>
                                                    <td class="text-left font-normal leading-none text-md">{{ withCurrency($value['gross'], null, \NumberFormatter::DECIMAL_SEPARATOR_SYMBOL) }}</td>
                                                </tr>
                                                @endforeach
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tfoot>
        </table>
        </div>

        @if($note)
        <div class="break-words mt-3">
            <p class="px-6 text-semibold text-grayDark leading-none">{{ __('pos_strings.pdf.reference_note') }}</p>
            <p class="px-6 text-semibold text-black leading-none mt-1">{{ $note }}</p>
        </div>
        @endif

        <hr class="border-purpleo18 my-6" />

        <div class="px-6">
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.session_id') }}: <span class="text-grayDim leading-none">{{ $session_id }}</span></p>
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.transaction_id') }}: <span class="text-grayDim leading-none">{{ $transaction_id }}</span></p>
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.transaction_type') }}: <span class="text-grayDim leading-none">{{ $transaction_type }}</span></p>
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.auth_code') }}: <span class="text-grayDim leading-none">{{ $auth_code }}</span></p>
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.retrieval_reference_number') }}: <span class="text-grayDim leading-none">{{ $retrieval_reference_number }}</span></p>
            @if($control_code && $ctu_id )
                <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.control_code') }}: <span class="text-grayDim leading-none">{{ $control_code }}</span></p>
                <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.ctu_id') }}: <span class="text-grayDim leading-none">{{ $ctu_id }}</span></p>
            @endif
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.pan_entry') }}: <span class="text-grayDim leading-none">{{ $pan_entry }}</span></p>
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.verification_method') }}: <span class="text-grayDim leading-none">{{ $verify_method }}</span></p>
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.iad') }}: <span class="text-grayDim leading-none">{{ $iad }}</span></p>
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.tvr') }}: <span class="text-grayDim leading-none">{{ $tvr }}</span></p>
            <p class="mb-1 font-medium leading-none text-grayDark">{{ __('pos_strings.pdf.tsi') }}: <span class="text-grayDim leading-none">{{ $tsi }}</span></p>
        </div>
    </main>
</body>

</html>
