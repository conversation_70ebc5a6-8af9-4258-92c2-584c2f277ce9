@php
    use App\Setting;
    use Carbon\Carbon;
@endphp
@props(['client','company'])

<div>
    <table>
        <tr>
            <td>
                <h2>{{ __('strings.ProfileDetails') }}</h2>
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th>{{ __('strings.FirstName') }}</th>
            <th>{{ __('strings.LastName') }}</th>
        </tr>
        <tr>
            <td>
                <p>{{ $client->first_name }}</p>
            </td>
            <td>
                <p>{{ $client->last_name }}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
        <tr>
            <th>{{ __('strings.DateBirth') }}</th>
            <th>{{ __('strings.Email') }}</th>
        </tr>
        <tr>
            <td>
                @php
                    try {
                @endphp
                        <p>{{ Setting::formateDate($company, Carbon::parse($client->social_security_number)) }}</p>
                @php
                    } catch (\Exception $e) {
                @endphp
                        <p>{{ $client->social_security_number }}</p>
                @php
                    }
                @endphp

            </td>
            <td>
                <p>{{ $client->email }}</p>
            </td>
        </tr>
    </table>
</div>
