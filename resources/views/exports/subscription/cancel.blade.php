<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Subscription Cancellation</title>
    <style>
        h2 {
            margin-bottom: 10px;
            text-decoration: underline;
        }

        body {
            font-family: sans-serif;
        }

        .spacediv {
            height: 20px;
            width: 100%;
        }

        .spacediv.small {
            height: 10px;
            width: 100%;
        }

        .tal {
            text-align: left;
        }

        table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }

        .tr_treatment.images,
        .tr_body {
            border-bottom: solid 20px transparent;
            box-shadow: 0px 1px 1px #eee;
        }

        table.data_table th,
        table.data_table td {
            padding: 6px;
        }

        table.data_table th {
            background: #eee;
            padding-top: 12px;
            padding-bottom: 12px;
        }

        table td,
        table th {
            text-align: left;
        }

        .tac {
            text-align: center;
        }

        .w100p {
            width: 100%;
        }

        body {
            color: #232323;
        }

        .text-gold {
            color: #BF9663;
        }

        .small-p {
            font-size: 13px;
        }

        .mid-p {
            font-size: 14px;
        }

        .p-18 {
            font-size: 18px;
        }

        .big-p {
            font-size: 20px;
        }

        .mt-0 {
            margin-top: 0px;
        }

        .mt-n5 {
            margin-top: -8px;
        }

        .grid4Table p {
            margin-bottom: 0px;
            font-weight: bold;
        }

        .grid4Table tr {
            border-top: solid 12px transparent;
        }

        .page-break {
            page-break-inside: avoid;
        }
    </style>
</head>
@php
    use App\Setting;
    use Carbon\Carbon;
@endphp

<body style="margin: 15px;">
    <table>
        <tr>
            <td>
                <h2>{{ __('strings.company_detail') }}</h2>
            </td>
        </tr>
    </table>
    <table>
        <tr>
            <th colspan="1">{{ __('strings.Name') }}</th>
            <th colspan="1">{{ __('strings.sign_up_mobile_number') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $company->company_name }}</p>
            </td>
            <td colspan="1">
                <p>{{ $company->mobile_number }}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.Email') }}</th>
            <th colspan="1">{{ __('strings.Country') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $company->email }}</p>
            </td>
            <td colspan="1">
                <p>{{ $company->country }}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <table>
        <tr>
            <td>
                <h2>{{ __('strings.plan_details') }}</h2>
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th colspan="1">{{ __('strings.Users') }}</th>
            <th colspan="1">{{ __('strings.price') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                @if ($subscription_cancellation_data->is_from_free_trail)
                    <p>{{ $company->user_count }}</p>
                @else
                    <p>{{ $subscription->quantity }}</p>
                @endif

            </td>
            <td colspan="1">
                @if ($subscription_cancellation_data->is_from_free_trail)
                    <p>{{ __('strings.free_trail') }}</p>
                @else
                    <p>{{ $plan->currency }} {{ $plan->cost_value * $subscription->quantity }} /
                        {{ $plan->is_yearly ? __('strings.year') : __('strings.month') }}</p>
                @endif

            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.plan_ends_at') }}</th>
            <th colspan="1">{{ __('strings.Country') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ Setting::formateDate($company, Carbon::parse($ends_at)) }}</p>
            </td>
            <td colspan="1">
                <p>{{ $company->country }}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <h2>{{ $subscription_cancellation_data->is_from_free_trail ? __('strings.free_trail_cancellation') : __('strings.subscription_cancellation') }}
    </h2>
    <div>
        <ol>
            @foreach ($subscription_cancellation_data->data as $data)
                <li class="page-break">
                    <p><b>{{ $data['question'] }}</b></p>
                    @if (!isset($data['type']))
                        <p class="mt-n5">{!! $data['answer'] !!}
                            {{ isset($data['more_info']) ? ' (' . $data['more_info'] . ')' : '' }}</p>
                    @else
                        @if ($data['type'] == 'CHECKBOX')
                            <p class="mt-n5">{!! $data['answer'] !!}</p>
                            <p class="mt-n5">
                                <input type="checkbox" checked>{!! $data['more_info'] !!}
                            </p>
                        @else
                            <p class="mt-n5">{!! $data['answer'] !!}
                                {{ isset($data['more_info']) ? ' (' . $data['more_info'] . ')' : '' }}</p>
                        @endif
                    @endif


                </li>
            @endforeach
        </ol>
    </div>
</body>

</html>
