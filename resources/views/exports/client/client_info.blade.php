<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        h2 {
            margin-bottom: 10px;
            text-decoration: underline;
        }

        body {
            font-family: sans-serif;
        }

        .spacediv {
            height: 20px;
            width: 100%;
        }

        .spacediv.small {
            height: 10px;
            width: 100%;
        }

        .tal {
            text-align: left;
        }

        table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }

        .tr_treatment.images,
        .tr_body {
            border-bottom: solid 20px transparent;
            box-shadow: 0px 1px 1px #eee;
        }

        table.data_table th,
        table.data_table td {
            padding: 6px;
        }

        table.data_table th {
            background: #eee;
            padding-top: 12px;
            padding-bottom: 12px;
        }

        table td,
        table th {
            text-align: left;
            word-wrap: break-word;
            word-break: break-word;
        }

        .tac {
            text-align: center;
        }

        .w100p {
            width: 100%;
        }
        td{
            vertical-align: top;
        }

        body {
            color: #232323;
        }

        .text-gold {
            color: #BF9663;
        }

        .small-p {
            font-size: 13px;
        }

        .mid-p {
            font-size: 14px;
        }

        .p-18 {
            font-size: 18px;
        }

        .big-p {
            font-size: 20px;
        }

        .mt-0 {
            margin-top: 0px;
        }

        .mt-n5 {
            margin-top: -8px;
        }

        .grid4Table p {
            margin-bottom: 0px;
            font-weight: bold;
        }

        .grid4Table tr {
            border-top: solid 12px transparent;
        }

        .text-break {
            word-break: break-all;
            word-wrap: break-word;
        }
    </style>
</head>
@php
    use App\Setting;
    use Carbon\Carbon;
@endphp
<body style="margin: 15px;">

    <table>
        <tr>
            <td>
                <h2>{{ __('strings.ProfileDetails') }}</h2>
            </td>
        </tr>
        <tr>
            <td class="tac w100p">
                @if ($client->profile_picture)
                    <img style="border-radius: 50%; margin: 0 auto;object-fit: cover;" height="120px" width="120px" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($client->profile_picture)) !!}" alt="">
                @endif
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th colspan="1">{{ __('strings.FirstName') }}</th>
            <th colspan="1">{{ __('strings.LastName') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $client->first_name }}</p>
            </td>
            <td colspan="1">
                <p>{{ $client->last_name }}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.DateBirth') }}</th>
            <th colspan="1">{{ __('strings.Email') }}</th>
            <th colspan="1">{{ __('strings.personal_number') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                @php
                    $should_format = 0;
                    try {
                        $should_format = 1;
                        $data = Carbon::parse($client->social_security_number);
                    } catch (\Throwable $th) {
                        $should_format = 0;
                    }
                @endphp
                @if ($should_format)
                    <p>{{ Setting::formateDate($company, Carbon::parse($client->social_security_number)) }}</p>
                @else
                    <p>{{ $client->social_security_number }}</p>
                @endif
            </td>
            <td colspan="1">
                <p>{{ $client->email }}</p>
            </td>
            <td colspan="1">
                <p>{{ $client->personal_id }}</p>
            </td>
        </tr>
        <tr>
            <td><div class="spacediv small"></div></td>
            <td><div class="spacediv small"></div></td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.PhoneNumber') }}</th>
            <th colspan="1">{{ __('strings.Address') }}</th>
            <th colspan="1">{{ __('strings.setting_country') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>+{{ $client->country_code }} {{ $client->phone_number }}</p>
            </td>
            <td colspan="1">
                <p>
                    {{ $client->pretty_address }}
                </p>
            </td>
            <td colspan="1">
                <p>
                    {{ $city }}
                </p>
            </td>
        </tr>
        <tr>
            <td><div class="spacediv small"></div></td>
            <td><div class="spacediv small"></div></td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.this_client_is_verified_by') }}</th>
            <th colspan="1">{{ __('strings.created_on') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                @if ($client->verification)
                    <p>{{ __('strings.id') }}: @if($client->verification->has_id) {{ __('strings.Yes') }} @else {{ __('strings.No') }} @endif</p>
                    <p>{{ __('strings.driving_license') }}: @if($client->verification->has_driving_license) {{ __('strings.Yes') }} @else {{ __('strings.No') }} @endif</p>
                    <p>{{ __('strings.passport') }}: @if($client->verification->has_passport) {{ __('strings.Yes') }} @else {{ __('strings.No') }} @endif</p>
                    <p>{{ __('strings.other') }}: @if($client->verification->other) {{ __('strings.Yes') }} @else {{ __('strings.No') }} @endif</p>
                    @if ($client->verification->note)
                        <p>{{ __('strings.NOTES') }}: {{ $client->verification->note }}</p>
                    @endif
                @else
                    <p>{{ __('strings.not_verified') }}</p>
                @endif
            </td>
            <td colspan="1">
                <p>{{ Setting::formateDate($company, Carbon::parse($client->created_at), true) }}</p>
            </td>
        </tr>
        <tr>
            <td><div class="spacediv small"></div></td>
            <td><div class="spacediv small"></div></td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.consent_approved') }}</th>
            <th colspan="1">{{ __('strings.next_of_kind') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>@if(isset($client->consent->verified_at) && $client->consent->verified_at) {{ __('strings.Yes') }} @else {{ __('strings.No') }} @endif</p>
            </td>
            <td colspan="1">
                <p>{{ __('strings.Name') }}: {{ $client->kind->name ?? '-' }}</p>
                @if($client->kind->email ?? false)<p>{{ __('strings.Email') }}: {{ $client->kind->email }}</p>@endif
                @if($client->kind->phone ?? false)<p>{{ __('strings.Phone') }}: {{ $client->kind->phone }}</p>@endif
                @if($client->kind->relation ?? false)<p>{{ __('strings.relation') }}: {{ $client->kind->relation }}</p>@endif
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
</body>

</html>
