<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <x-pdf-css />
</head>
@php
    use App\Setting;
    use Carbon\Carbon;
@endphp
<body style="margin: 15px;">

    <x-pdf-client-details :client="$client" :company="$company"/>
    <div class="spacediv"></div>
    <h2>{{ $note->title }}</h2>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th>{{ __('strings.SignedBy') }}</th>
            <th>{{ __('strings.SignedAt') }}</th>
        </tr>
        <tr>
            <td>
                @if ($note->sign)
                    <p><b>{{ $note->signed_by->first_name }} {{ $note->signed_by->last_name }}</b></p><br />
                    <img width="50%" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($note->sign)) !!}" alt="Signature">
                @else
                    Not Signed
                @endif
            </td>
            <td>
                @if ($note->sign)
                    <p>{{ Setting::formateDate($company, Carbon::parse($note->signed_at), true) }}</p>
                @else
                    Not Signed
                @endif
            </td>
        </tr>
    </table>

    <div class="spacediv"></div>
    <p><b>{{ __('strings.NOTES') }}</b></p>
    @if ($note->notes_html)
        <p>{!! $note->notes_html !!}</p>
    @else
        <p>{{ $note->notes }}</p>
    @endif

    <div class="spacediv"></div>
    <p><b>{{ __('strings.files') }}</b></p>
    @if (count($note->files))
        @foreach ($note->files as $index => $file)
            @if ($note->filenames && is_array($note->filenames))
                <p>{{ count($note->filenames) ? $note->filenames[$index] : ($note->filename ? $note->filename : '-') }}</p>
            @endif
        @endforeach
    @else
        <p>No Files</p>
    @endif
</body>

</html>
