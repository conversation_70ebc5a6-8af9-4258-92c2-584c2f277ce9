<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        h2 {
            margin-bottom: 10px;
            text-decoration: underline;
        }

        body {
            font-family: sans-serif;
        }

        .spacediv {
            height: 20px;
            width: 100%;
        }

        .spacediv.small {
            height: 10px;
            width: 100%;
        }

        .tal {
            text-align: left;
        }

        table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }

        .tr_treatment.images,
        .tr_body {
            border-bottom: solid 20px transparent;
            box-shadow: 0px 1px 1px #eee;
        }

        table.data_table th,
        table.data_table td {
            padding: 6px;
        }

        table.data_table th {
            background: #eee;
            padding-top: 12px;
            padding-bottom: 12px;
        }

        table td,
        table th {
            text-align: left;
            word-wrap: break-word;
            word-break: break-word;
        }

        .tac {
            text-align: center;
        }

        .w100p {
            width: 100%;
        }
        td{
            vertical-align: top;
        }

        body {
            color: #232323;
        }

        .text-gold {
            color: #BF9663;
        }

        .small-p {
            font-size: 13px;
        }

        .mid-p {
            font-size: 14px;
        }

        .p-18 {
            font-size: 18px;
        }

        .big-p {
            font-size: 20px;
        }

        .mt-0 {
            margin-top: 0px;
        }

        .mt-n5 {
            margin-top: -8px;
        }

        .grid4Table p {
            margin-bottom: 0px;
            font-weight: bold;
        }

        .grid4Table tr {
            border-top: solid 12px transparent;
        }

        .text-break {
            word-break: break-all;
            word-wrap: break-word;
        }
    </style>
</head>
@php
    use App\Setting;
    use Carbon\Carbon;
@endphp


<body style="margin: 15px;">

    <table>
        <tr>
            <td>
                <h2>{{ __('strings.booking_detail') }}</h2>
            </td>
        </tr>
    </table>
    {{-- <div class="spacediv"></div> --}}
    <table>
        <tr>
            <th colspan="1">{{ __('strings.pdf_service') }}</th>
            <th colspan="1">{{ __('strings.pdf_booked_time_slot') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $booking->service->name }}</p>
            </td>
            <td colspan="1">
                <p>{{ Setting::formateDate($company, Carbon::parse($booking->start_at), true) }}</p>
            </td>
        </tr>
        {{-- <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr> --}}
        <tr>
            <th colspan="1">{{ __('strings.pdf_client_name') }}</th>
            <th colspan="1">{{ __('strings.pdf_client_email') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $booking->client->first_name }} {{ $booking->client->last_name }}</p>
            </td>
            <td colspan="1">
                <p>{{ $booking->client->email }}</p>
            </td>
        </tr>
        <tr>
            <td><div class="spacediv"></div></td>
            <td><div class="spacediv"></div></td>
        </tr>

        @if ($booking->user)
        <tr>
            <th colspan="1">{{ __('strings.pdf_practitioner_name') }}</th>
            <th colspan="1">{{ __('strings.pdf_practitioner_email') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $booking->user->first_name }} {{ $booking->user->last_name }}</p>
            </td>
            <td colspan="1">
                <p>{{ $booking->user->email }}</p>
            </td>
        </tr>
        @endif
        <tr>
            <th colspan="1">{{ __('strings.pdf_special_request') }}</th>
            <th colspan="1">{{ __('strings.pdf_is_client_shown') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $booking->special_request }} </p>
            </td>
            <td colspan="1">
                <p>{{ $booking->is_shown ? __('strings.Yes') : __('strings.No') }} </p>
            </td>
        </tr>

        <tr>
            <th colspan="1">{{ __('strings.pdf_is_booking_cancelled') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $booking->is_cancelled ? __('strings.Yes') : __('strings.No') }} </p>
            </td>
        </tr>

    </table>
    <div class="spacediv"></div>
</body>

</html>
