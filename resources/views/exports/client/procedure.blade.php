<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <x-pdf-css />
</head>
@php
    use App\Setting;
    use Carbon\Carbon;
@endphp
<body style="margin: 15px;">

    <x-pdf-client-details :client="$client" :company="$company"/>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th>{{ __('strings.SignedBy') }}</th>
            <th>{{ __('strings.SignedAt') }}</th>
            <th>{{ __('strings.CreatedAt') }}</th>
        </tr>
        <tr>
            <td>
                @if ($treatment->sign && $treatment->signed_by)
                    <p><b>{{ $treatment->signed_by->first_name }} {{ $treatment->signed_by->last_name }}</b></p><br />
                    <img width="24%" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($treatment->sign)) !!}" alt="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($treatment->sign)) !!}">
                @else
                    <b>Not Signed</b>
                @endif
            </td>
            <td>
                @if ($treatment->sign && $treatment->signed_by)
                    <p><b>{{ Setting::formateDate($company, Carbon::parse($treatment->signed_at), true) }}</b></p>
                @else
                    <b>Not Signed</b>
                @endif
            </td>
            <td>
                @if ($treatment->created_at)
                    <p><b>{{ Setting::formateDate($company, Carbon::parse($treatment->created_at), true) }}</b></p>
                @endif
            </td>
        </tr>
    </table>
    <h2>{{ $treatment->name }}</h2>
    <table class="data_table">
        <tr>
            <th>{{ __('strings.nrs_rating') }}</th>
            <th>{{ __('strings.Date') }}</th>
            <th>{{ __('strings.COST') }}</th>
            <th>{{ __('strings.TemplateName') }}</th>
        </tr>
        <tr class="tr_treatment">
            <td>{{ $treatment->nrs_rating??'-' }}</td>
            <td>{{ Setting::formateDate($company, Carbon::parse($treatment->date), true) }}</td>Not Signed
            <td class="text-break">
                <b>{{ __('strings.master_ProductsTreatments') }}</b>
                <br>
                @foreach ($treatment->details as $detail)
                    <p>{{ $detail->name . ' (' . $detail->unitSymbol() . ' ' . $detail->actual_cost . ')' }}</p>
                @endforeach
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <b>{{ __('strings.NOTES') }}: </b>
    @if ($treatment->notes_html)
        {!! $treatment->notes_html !!}
    @else
        {{ $treatment->notes }}
    @endif

    <div class="spacediv"></div>
    <div class="tr_treatment images">
        @foreach ($treatment->images as $image)
            <img width="33%" style="border: solid 1px #eee;" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($image)) !!}" alt="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($image)) !!}" />
        @endforeach
    </div>
</body>

</html>
