<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <x-pdf-css />
</head>
@php
    use App\Setting;
    use Carbon\Carbon;
@endphp
<body style="margin: 15px;">

    <x-pdf-client-details :client="$client" :company="$company"/>
    <div class="spacediv"></div>
    <h2>{{ $letter_of_consent->consent_title }}</h2>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th>{{ __('strings.SignedBy') }}</th>
            <th>{{ __('strings.SignedAt') }}</th>
        </tr>
        <tr>
            <td>
                @if ($letter_of_consent->verified_sign)
                    <p><b>{{ $letter_of_consent->verified_signed_by->first_name }} {{ $letter_of_consent->verified_signed_by->last_name }}</b></p><br />
                    <img width="50%" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($letter_of_consent->verified_sign)) !!}" alt="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($letter_of_consent->verified_sign)) !!}">
                @else
                    Not Signed
                @endif
            </td>
            <td>
                @if ($letter_of_consent->verified_sign)
                    <p><b>{{ Setting::formateDate($company, Carbon::parse($letter_of_consent->verified_signed_at), true) }}</b></p>
                @else
                    Not Signed
                @endif
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th>{{ __('strings.Date') }}</th>
            <th>{{ __('strings.AllowShare') }}</th>
        </tr>
        <tr>
            <td>{{ Setting::formateDate($company, Carbon::parse($letter_of_consent->created_at), true) }}</td>
            <td>{{ $letter_of_consent->is_publish_before_after_pictures == '1' ? __('strings.Yes') : __('strings.No') }}</td>
        </tr>
    </table>

    <div class="spacediv"></div>
    <p>{!! '<b> ' . __('strings.Letter') . '</b><br />' . $letter_of_consent->letter !!}</p>
    <div class="spacediv"></div>
</body>

</html>
