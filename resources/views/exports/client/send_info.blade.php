<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <x-pdf-css />
</head>
@php
    use App\Setting;
    use Carbon\Carbon;
@endphp
<body style="margin: 15px;">

    <x-pdf-client-details :client="$client" :company="$company"/>
    <div class="spacediv"></div>
    <table style="width: 100%">
        <tr class="tr_treatment">
            <th style="width: 75%">Details</th>
            <th style="width: 25%">Date Time</th>
        </tr>
        @foreach ($logs as $log)
            <tr style="page-break-inside: avoid;" class="tr_treatment">
                <td style="width: 75%">
                    <p style="overflow-wrap: break-word">{{$log->description}}</p>
                </td>
                <td style="width: 25%">
                    <p>{{ Setting::formateDate($company, Carbon::parse($log->created_at)->timezone(request()->header('X-Time-Zone','UTC')), true) }}</p>
                </td>
            </tr> 
        @endforeach
    </table>
    <div class="spacediv"></div>
</body>

</html>
