@php
    use Carbon\Carbon;
    use App\Setting;
@endphp
<table>
    <thead>
        <tr>
            <th>{{ __('pos_strings.gift_card_number') }}</th>
            <th>{{ __('pos_strings.gift_card_status') }}</th>
            <th>{{ __('pos_strings.gift_card_validity') }}</th>
            <th>{{ __('pos_strings.gift_card_customer') }}</th>
            <th>{{ __('pos_strings.gift_card_remaining_value') }}</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($gift_cards as $gift_card)
        <tr>
           <td>{{ $gift_card->padded_gift_code }}</td>
           <td>{{ ucfirst($gift_card->status) }}</td>
           <td>{{ Setting::formateDate($company, Carbon::parse($gift_card->created_at), false)}} TO {{Setting::formateDate($company, Carbon::parse($gift_card->expiry_date), false) }}</td>
           <td>{{ $gift_card?->client?->full_name ?? ''  }}</td>
           <td>{{ $gift_card->current_value }} / {{$gift_card->initial_value }}</td>
        </tr>
        @endforeach
    </tbody>
</table>
