<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        h2 {
            margin-bottom: 10px;
            text-decoration: underline;
        }

        body {
            font-family: sans-serif;
        }

        .spacediv {
            height: 20px;
            width: 100%;
        }

        .spacediv.small {
            height: 10px;
            width: 100%;
        }

        .tal {
            text-align: left;
        }

        table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }

        .tr_treatment.images,
        .tr_body {
            border-bottom: solid 20px transparent;
            box-shadow: 0px 1px 1px #eee;
        }

        table.data_table th,
        table.data_table td {
            padding: 6px;
        }

        table.data_table th {
            background: #eee;
            padding-top: 12px;
            padding-bottom: 12px;
        }

        table td,
        table th {
            text-align: left;
        }

        .tac {
            text-align: center;
        }

        .w100p {
            width: 100%;
        }

        body {
            color: #232323;
        }

        .text-gold {
            color: #BF9663;
        }

        .small-p {
            font-size: 13px;
        }

        .mid-p {
            font-size: 14px;
        }

        .p-18 {
            font-size: 18px;
        }

        .big-p {
            font-size: 20px;
        }

        .mt-0 {
            margin-top: 0px;
        }

        .mt-n5 {
            margin-top: -8px;
        }

        .grid4Table p {
            margin-bottom: 0px;
            font-weight: bold;
        }

        .grid4Table tr {
            border-top: solid 12px transparent;
        }
        .page-break{
            page-break-inside: avoid;
        }
    </style>
</head>

<body style="margin: 15px;">
<table>
        <tr>
            <td>
                <h2>{{ __('strings.ProfileDetails') }}</h2>
            </td>
        </tr>
        <tr>
            <td class="tac w100p">
                @if($client->profile_picture)
                <img style="border-radius: 50%; margin: 0 auto;object-fit: cover;" height="120px" width="120px"
                    src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($client->profile_picture)) !!}" alt="">
                @endif
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th colspan="1">{{ __('strings.FirstName') }}</th>
            <th colspan="1">{{ __('strings.LastName') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{$client->first_name}}</p>
            </td>
            <td colspan="1">
                <p>{{$client->last_name}}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.DateBirth') }}</th>
            <th colspan="1">{{ __('strings.Email') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{$client->social_security_number}}</p>
            </td>
            <td colspan="1">
                <p>{{$client->email}}</p>
            </td>
        </tr>
        @if ($client->personal_id)
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.personal_number') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{$client->personal_id}}</p>
            </td>
        </tr>
        @endif

        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
    </table>
    <h2>{{ __('strings.HealthQuestionnaire') }} - {{ $created_at->timezone(request()->header('X-Time-Zone','UTC'))->format('Y-m-d H:i:s') }}</h2>
    <div>
        <ol>
            @foreach($datas as $index => $data)
            @if(isset($data['answer']))
            <li class="page-break">
                <p><b>{{__("strings.health_question_new1_".$index)}}</b></p>
                <p class="mt-n5">{{ __("strings.".$data['answer']) }}</p>
                @if(isset($data['more_info']))
                <p class="mt-n5">{{ __('strings.more_info')}} : {{$data['more_info']}}</p>
                @endif
            </li>
            @endif
            @if (isset($data['more_info']))
                <p><b>{{__("strings.more_info")}}</b></p>
                <p class="mt-n5">{{ $data['more_info'] }}</p>
            @endif
            @endforeach
        </ol>
    </div>
</body>

</html>
