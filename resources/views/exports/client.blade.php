<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        h2 {
            margin-bottom: 10px;
            text-decoration: underline;
        }

        body {
            font-family: sans-serif;
        }

        .spacediv {
            height: 20px;
            width: 100%;
        }

        .spacediv.small {
            height: 10px;
            width: 100%;
        }

        .tal {
            text-align: left;
        }

        table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }

        .tr_treatment.images,
        .tr_body {
            border-bottom: solid 20px transparent;
            box-shadow: 0px 1px 1px #eee;
        }

        table.data_table th,
        table.data_table td {
            padding: 6px;
        }

        table.data_table th {
            background: #eee;
            padding-top: 12px;
            padding-bottom: 12px;
        }

        table td,
        table th {
            text-align: left;
            word-wrap: break-word;
            word-break: break-word;
        }

        .tac {
            text-align: center;
        }

        .w100p {
            width: 100%;
        }

        body {
            color: #232323;
        }

        .text-gold {
            color: #BF9663;
        }

        .small-p {
            font-size: 13px;
        }

        .mid-p {
            font-size: 14px;
        }

        .p-18 {
            font-size: 18px;
        }

        .big-p {
            font-size: 20px;
        }

        .mt-0 {
            margin-top: 0px;
        }

        .mt-n5 {
            margin-top: -8px;
        }

        .grid4Table p {
            margin-bottom: 0px;
            font-weight: bold;
        }

        .grid4Table tr {
            border-top: solid 12px transparent;
        }

        .text-break{
            word-break: break-all;
            word-wrap: break-word;
        }
    </style>
</head>

<body style="margin: 15px;">

    <table>
        <tr>
            <td>
                <h2>{{ __('strings.ProfileDetails') }}</h2>
            </td>
        </tr>
        <tr>
            <td class="tac w100p">
                @if($client->profile_picture)
                    <img style="border-radius: 50%; margin: 0 auto;" height="120px" width="120px" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($client->profile_picture)) !!}" alt="">
                @endif
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th colspan="1">{{ __('strings.FirstName') }}</th>
            <th colspan="1">{{ __('strings.LastName') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{$client->first_name}}</p>
            </td>
            <td colspan="1">
                <p>{{$client->last_name}}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.DateBirth') }}</th>
            <th colspan="1">{{ __('strings.Email') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{$client->social_security_number}}</p>
            </td>
            <td colspan="1">
                <p>{{$client->email}}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.PhoneNumber') }}</th>
            <th colspan="1">{{ __('strings.Address') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{$client->phone_number}}</p>
            </td>
            <td colspan="1">
                <p>@if(count($client->addresses)){{
                    $client->addresses[0]->street_address.", ".
                    $client->addresses[0]->city.", ".
                    $client->addresses[0]->zip_code.", ".
                    $client->addresses[0]->state.", ".
                    $client->addresses[0]->country
                }}@endif</p>
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <h2>{{ __('strings.master_LettersOfConsents') }}</h2>
    <table class="data_table">
        <tr>
            <th>{{ __('strings.TITLE') }}</th>
            {{-- <th>Info</th> --}}
            <th>{{ __('strings.Sign') }}</th>
            <th>{{ __('strings.Date') }}</th>
            <th>{{ __('strings.AllowShare') }}</th>
        </tr>
        @foreach($client->letter_of_consents as $letter_of_consent)
        <tr>
            <td>{{ $letter_of_consent->consent_title }}</td>
            <td>
                @if($letter_of_consent->signature)
                <img width="50%" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($letter_of_consent->signature)) !!}" alt="Signature">
                @endif
            </td>
            <td>{{ Carbon\Carbon::parse($letter_of_consent->created_at)->format('Y-m-d') }}</td>
            <td>{{ $letter_of_consent->is_publish_before_after_pictures == '1' ? __('strings.Yes') : __('strings.No') }}</td>
        </tr>
        <tr class="tr_body">
            <td colspan="4">{!! "<b> ". __('strings.Letter') . "</b><br />" . $letter_of_consent->letter !!}</td>
        </tr>
        @if ($letter_of_consent->verified_sign)
            <tr class="tr_body">
                <td colspan="4">
                    <b>{{ __('strings.Verified_Sign_by') }} {{ $letter_of_consent->verified_signed_by->first_name }} {{ $letter_of_consent->verified_signed_by->last_name }} {{ __('strings.at') }} {{ $letter_of_consent->verified_signed_at }}</b><br />
                    <img width="24%" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($letter_of_consent->verified_sign)) !!}" alt="">
                </td>
            </tr>
        @endif
        @endforeach
    </table>
    <div class="spacediv"></div>
    <h2>{{ __('strings.Procedures') }}</h2>
    <table class="data_table">
        <tr>
            <th>{{ __('strings.Date') }}</th>
            <th>{{ __('strings.COST') }}</th>
            <th>{{ __('strings.TemplateName') }}</th>
        </tr>
        @foreach($client->treatments as $treatment)
        <tr class="tr_treatment">
            <td>{{ $treatment->date }}</td>
            <td>{{ $treatment->cost }}</td>
            <td class="text-break">
                {{ $treatment->name }}

                <b>{{ __('strings.master_ProductsTreatments') }}</b>
                <br>
                @if ($treatment->details->count())
                    {{
                        $treatment->details->map(function ($detail) {
                            return $detail->name . " (" . $detail->unitSymbol() ." " . $detail->actual_cost . ")";
                        })->join("\n")
                     }}
                @endif
            </td>
        </tr>
        <tr class="tr_treatment">
            <td class="text-break" colspan="4"><b>{{ __('strings.NOTES') }}: </b> @if ($treatment->notes_html)
                {!! $treatment->notes_html !!}
            @else
                {{ $treatment->notes }}
            @endif </td>
        </tr>
        <tr class="tr_treatment images">
            <td colspan="4">
                @foreach($treatment->images as $image)
                <img width="24%" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($image)) !!}" alt="">
                @endforeach
            </td>
        </tr>
        @if ($treatment->sign && $treatment->signed_by)
            <tr class="tr_body">
                <td colspan="4">
                    <b>{{ __('strings.Verified_Sign_by') }} {{ $treatment->signed_by->first_name }} {{ $treatment->signed_by->last_name }} {{ __('strings.at') }} {{ $treatment->signed_at }}</b><br />
                    <img width="24%" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($treatment->sign)) !!}" alt="">
                </td>
            </tr>
        @endif
        @endforeach
    </table>
    <div class="spacediv"></div>
    @if(count($client->health_questionaries))
    <h2>{{ __('strings.HealthQuestionnaire') }}</h2>
    <div>
        <ol>
            @foreach($client->health_questionaries as $health_question)
            @if(is_array($health_question->data_new))
            @foreach($health_question->data_new as $index => $data)
            @if(isset($data->answer))
            <li>
                <p><b>{{__("strings.health_question_new1_".$index)}}</b></p>
                <p class="mt-n5">{{__("strings.".$data->answer)}}</p>
                @if(isset($data->more_info))
                <p class="mt-n5">{{ __('strings.more_info') }}: {{$data->more_info}}</p>
                @endif
            </li>
            @endif
            @endforeach
            @endif
            @endforeach
        </ol>
    </div>
    @endif
    @if(count($client->aesthetic_insterests))
    <div class="spacediv"></div>
    <h2>{{__("strings.Aestethicinterest")}}</h2>
    <ol>
        @foreach($client->aesthetic_insterests as $aesthetic_insterest)
        @if(isset($aesthetic_insterest->data_new['aesthetic_interest']) && is_array($aesthetic_insterest->data_new['aesthetic_interest']))
        @foreach($aesthetic_insterest->data_new['aesthetic_interest'] as $index => $data)
        @if(isset($data['notes']))
        <li>
            <h5><b>{{__("strings.aestethic_question_new1_".$index)}}</b>
                <h5>
                    <div>
                        <p>{{$data['notes']}}</p>
                    </div>
        </li>
        @endif

        @if(isset($data['answer_radio']))
        <li>
            <div>
                <h5><b>{{__("strings.aestethic_question_new1_".$index)}}</b> -
                    <b>{{ $data['answer_radio'] ? __('strings.'.$data['answer_radio']) : '' }}</b>
                    <h5>
            </div>
        </li>
        @endif

        @if(isset($data['answer_checkbox']))
        <li>
            <div>
                <h5><b>{{__("strings.aestethic_question_new1_".$index)}}</b></h5>
                @foreach($data['answer_checkbox'] as $innerIndex => $answer_checkbox)
                @if($answer_checkbox == '1')
                <div>
                    <label>{{__("strings.aestethic_question_new1_".$index."_".$innerIndex)}}</label>
                </div>
                @endif
                @endforeach
            </div>
        </li>
        @endif
        @if(isset($data['other']))
        {{ __('strings.Other') }} : {{ $data['other'] }}
        @endif

        @if(isset($data['image']))
        <li>
            <div class="pt-3">
                <h5><b>{{__("strings.aestethic_question_new1_".$index)}}</b></h5>
                <img class="mx-auto" width="100%" style="max-width: 760px;" src="{!! urldecode($data['image']) !!}" alt="">
            </div>
        </li>
        @endif
        @endforeach
        @endif
        @endforeach
    </ol>
    @endif

    @if(count($client->covid19s))
    <h2>{{ __('strings.Covid19Questionnaire') }}</h2>
    <div>
        <ol>
            @foreach($client->covid19s as $covid19)
            @if(is_array($covid19->data))
            @foreach($covid19->data as $index => $data)
            @if(isset($data->answer))
            <li>
                <p><b>{{__("strings.covid19_".$index)}}</b></p>
                <p class="mt-n5">{{__("strings.".$data->answer) }}</p>
                @if(isset($data->more_info))
                <p class="mt-n5">{{ __('strings.more_info') }}: {{$data->more_info}}</p>
                @endif
            </li>
            @endif
            @endforeach
            @endif
            @endforeach
        </ol>
    </div>
    @endif
    <div class="spacediv"></div>

    @if(count($client->general_notes))
    <h2>{{ __('strings.general_note') }}</h2>
    <table class="data_table">
        <tr>
            <th>{{ __('strings.TITLE') }}</th>
            <th>{{ __('strings.SignedAt') }}</th>
            <th>{{ __('strings.Sign') }}</th>
        </tr>
        @foreach($client->general_notes as $note)
        <tr>
            <td>{{ $note->title }}</td>
            <td>{{ $note->signed_at ?? '' }}</td>
            <td>
                @if ($note->sign)
                    <b>{{ __('strings.Verified_Sign_by') }} {{ $note->signed_by->first_name ?? '' }} {{ $note->signed_by->last_name ?? '' }} {{ __('strings.at') }} {{ $note->signed_at }}</b><br />
                    <img width="100%" src="{!! urldecode(Illuminate\Support\Facades\Storage::disk('s3')->url($note->sign)) !!}" alt="">
                @endif
            </td>
        </tr>
        <tr class="tr_body">
            <td colspan="4"><br /><b>{{ __('strings.NOTES') }}</b><br />
                @if ($note->notes_html)
                    {!! $note->notes_html !!}
                @else
                    {{ $note->notes }}
                @endif
            </td>
        </tr>
        @endforeach
    </table>
    @endif

    <div class="spacediv"></div>
</body>

</html>
