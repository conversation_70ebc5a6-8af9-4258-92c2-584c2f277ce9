<?php

use App\CompanyGiftCard;
use App\CompanyReceipt;

return [
    'customer_need_to_connect' => 'Customer needs to be connected successfully',
    'customer_connected' => "Customer connected successfully",

    'pos_applied_success' => "Applied to POS successfully.",

    "swish_qr_uploaded_successfully" => "Swish QR uploaded susccessfully.",

    //13/09/2023 (MONIKA)
    'company_product_active' => 'Company product active',
    'company_product_in_active' => 'Company product in-active',
    'company_product_category_active' => 'Company product category active',
    'company_product_category_in_active' => 'Company product category in-active',

    //22/09/2023 (MONIKA)
    'no_data_found' => 'Data not found',

    //02/10/2023( MONIKA)
    'terminal_device_updated' => 'Terminal Device Updated successfully',
    "company_gift_card_return" => "Company Gift card returned",

    //06-10-2023 (MONIKA)
    "gift_card_expired" => 'The gift card has expired',
    "invalid_gift_card" => "The gift card is invalid",

    // 11-10-2023 (<PERSON><PERSON>)
    "payment_request_has_been_sent" => "Payment request has been sent",
    "gift_card_amount_should_be_less_than_sub_total" => "Gift card amount should be less than sub total",
    "discount_should_be_less_than_sub_total" => "Discount should be less than sub total",
    "company_terminals_list_return" => "Company terminals list return successfully",
    "company_terminal_updated" => "Company terminal updated successfully",
    "company_gift_list_return" => "Company gift card list return successfully",
    "no_category_available" => "No category available",

    //03-11-2023
    "quantity_not_available_for_refund" => "Quantity not available for refund.",
    "gift_card_amount_cannot_be_greater_than_available_amount" => "Gift card amount is greater than available amount",
    "refund_amount_cannot_be_greater_than_available_amount" => "Refund amount is greater than available amount",
    "refundable_amount_is_greater_than_total_amount" => "Refundable amount is greater than total amount",
    "refund_request_has_been_sent" => "refund request has been sent",
    "discount_should_be_less_than_total" => "Discount should be less than total",
    "gift_card_amount_should_be_less_than_payable_amount" => "Gift card amount should be less than payable amount",
    "payment_completed" => "Payment is completed",
    "terminal_is_required" => "Terminal device is required",
    "invalid_terminal_device" => "Invalid Terminal device, try again with different terminal",
    "company_receipt_returned_success" => "Company receipt returned successfully",
    "company_receipt_processed_for_retry" => "Company receipt processed for retry",
    "company_receipt_cannot_aborted" => "Company receipt cannot aborted",

    "company_product_receipt_list_return" => "Company product receipt list return successfully",
    "company_receipt_refund_returned_success" => "Company receipt refund returned successfully",
    "company_receipt_refund_processed_for_retry" => "Company receipt refund processed for retry",
    "company_receipt_refund_aborted" => "Company receipt refund aborted",
    "company_receipt_refund_cannot_aborted" => "Company receipt refund cannot aborted",
    "invalid_product_category" => "Invalid product category",
    "invalid_product" => "Invalid Product",
    "invalid_receipt" => "Invalid Receipt",
    "company_dont_have_merchant_id" => "Company don't have merchant id",
    "cannot_export_receipt" => "cannot export receipt",
    "cannot_retry_this_payment" => "Cannot retry this payment",
    "cannot_abort_this_receipt" => "Cannot abort this receipt",
    "cannot_refund" => "Cannot refund",
    "invalid_refund_receipt" => "invalid refund receipt",
    "cannot_retry_this_refund" => "cannot retry this refund",
    "invalid_terminal" => "Invalid terminal",

    "company_product_list_return" => "Company product list return successfully",
    "company_product_created" => "Company product created successfully",
    "company_product_updated" => "Company product updated successfully",

    "company_product_category_list_return" => "Company product category list return successfully",
    "company_product_category_created" => "Company product category created successfully",
    "company_product_category_updated" => "Company product category updated successfully",

    "company_gift_card_receipt_created" => "Company Gift Card Created",
    "company_gift_card_receipt_updated" => "Company Gift Card Updated Successfully",
    "merchant_id_is_required" => "Company required merchant ID",
    "requested_amount_exceed_available_balance" => "Requested amount exceed available balance",

    //25-09-2021 (Monika)
    "product_id" => "ID",
    "product_barcode" => "EAN/UPC",
    "product_name" => "Product Name",
    "product_category" => "Category",
    "product_description" => "Description",
    "product_selling_price" => "Selling Price",
    "product_base_price" => "Base Price",
    "product_tax_information" => "Tax Information",
    "product_stock" => "Stock",
    "product_status" => "Status",

    //05-10-2023(Monika)
    "gift_card_number" => "Card Number",
    "gift_card_status" => "Status",
    "gift_card_paid_at" => "Paid At",
    "gift_card_start_date" => "Start Date",
    "gift_card_end_date" => "End Date",
    "gift_card_customer" => "Customer",
    "gift_card_initial_value" => "Initial Value",
    "gift_card_remaining_value" => "Remaining Value",


    //22-11-2023
    "viva_amount_cannot_be_greater_than" => "payable amount can not be greater than :maximum_amount",
    "viva_refundable_amount_cannot_be_greater_than" => "refundable amount can not be greater than :maximum_amount",

    "products" => "Products",

    "pdf" => [
        "receipt" => "RECEIPT",
        "cash_invoice" => "CASH INVOICE",
        "title_receipt_copy" => "RECEIPT COPY",
        "org_no" => "org. no",
        "from" => "From",
        "billed_to" => "Billed to",
        "receipt_no" => "Receipt No.",
        "order" => "ORDER",
        "price" => "PRICE",
        "unit" => "UNIT",
        "vat" => "VAT",
        "id" => "Id",
        "payment" => "Payment",
        "credit_debit_card" => "Credit/Debit Card",
        "trxn_id" => "TrxnId",
        "merchant_id" => "Merchant ID",
        "terminal_id" => "Terminal ID",
        "ecr_id" => "ECR ID",
        "gift_card_no" => "Gift Card No",
        "card_no" => "Card no",
        "sub_total" => "SUBTOTAL",
        "summary_sub_total" => " Subtotal",
        "summary_vat" => "VAT",
        "total" => "TOTAL",
        "powered_by" => "Powered By",
        "thank_you_message" => "Thank You For Your Business!",
        "reference_note" => "Reference Note",
        "payment_type" => "Payment Type",
        "transaction_type" => "Transaction Type",
        "offline" => "Offline",
        "online" => "Online",

        "refund_receipt" => "REFUND RECEIPT",
        "refund_to" => "Refund To",
        "refund_payment" => "Refund Payment",
        "refund_subtotal" => "Refund Subtotal",
        "refund_total" => "Refund Total",

        "print_date" => "Print Date",
        "ecr_name_and_version" => "ECR name and version",
        "z_report_id" => "Z report ID",
        "ecr_id" => "ECR ID",
        "carried_out" => "Carried out",
        "receipts" => "Receipts",

        "sale" => "Sale",
        "total_sale" => "Total Sale",
        "qty" => "Qty.",
        "excluding_vat" => "Excluding VAT",
        "including_vat" => "Including VAT",
        "services" => "Services",
        "gift_cards" => "Gift Cards",
        "gift_card" => "Gift Card",
        "discount" => "Discount",
        "returns" => "Returns",
        "cancellation" => "Cancellations",
        "title_refund_receipt_copy" => "REFUND RECEIPT COPY",

        "session_id" => "Session Id",
        "transaction_id" => "Transaction Id",
        "auth_code" => "Auth Code",
        "retrieval_reference_number" => "Retrieval Reference Number",
        "control_code" => "Control Code",
        "ctu_id" => "CTUID",
        "pan_entry" => "Pan Entry",
        "verification_method" => "Verification Method",
        "iad" => "IAD",
        "tvr" => "TVR",
        "tsi" => "TSI",
        "tax" => "VAT",
        "net" => "Net",
        "gross" => "Gross",
        "receipt_copy" => "Receipt Copy",
        "total" => "Total",

        "grand_total" => "Grand Total",
        "sales_inc_returns" => "Sales including returns",
        "sales_exc_returns" => "Sales excluding returns",
        "grand_return" => "Grand Returns",
        "grand_net" => "Grand Net",

        "payment_method" => "Payment Method",
        "other" => "Other",
        "amount" => "Amount",
    ],

    "z_report" => "Z-Report",
    "x_report" => "X-Report",

    "mail" => [
        "z_report_for_date" => "Z-Report for :date",
        "z_report_date" => "Z-Report - :date",
        "hope_message_find_you" => "I trust this message finds you well. Your continued support means a lot to us.",
        "attacted_z_report_for_date" => "Please find attached the Z report for :date.",
        "take_moment_to_review" => "If you have any questions or need further clarification, please do not hesitate to get in touch.",
        "merchant_data_verification" => "Merchant data verification",
        "mail_sent_successfully" => "Mail sent successfully",
        "pos_activation_title" => "Activated Merchant ID for POS System Access",

        "your_merchant_id" => "Your merchant id:",
        "your_merchant_id_is_activated" => "Your Merchant ID has been successfully activated! This means you can start using POS system on our platform.",
        "merchant_data" => "Merchant Company Data",
        "details_of_the_merchant_data" => "Below are the details of the merchant data we have submitted for verification:",
        "merchant_id_updated" => "Merchant id updated",
        "your_merchant_id_is_updated" => "Your Merchant ID has been successfully updated!",

        "receipt_for_date" => ":company_name Receipt :date",
        "receipt_date" => "Receipt - :date",
        "attacted_receipt_for_date" => "Attached is the receipt detailing the transactions and figures for :date",
        "cannot_answer" => "This email cannot be answered.",

        "refund_receipt_for_date" => "Refund Receipt for :date",
        "refund_receipt_date" => "Refund Receipt - :date",
        "attacted_refund_receipt_for_date" => "Attached, you will find the refund receipt detailing the transactions and figures for :date",
    ],

    "swish" => [
        "swish" => "Swish",
    ],

    "viva" => [
        "viva" => "Viva",
        "viva_online" => "Viva Online",
        "internal_server_error" => "Internal Server Error",
        "session_is_being_processed" => "The session is being processed",
        "request_error" => "Request Error",
        "unauthorized" => "Unauthorized",
        "invalid_cash_register" => "Invalid cash register",
        "session_not_found" => "Session not found",
        "abort_process_already_started" => "Abort process already started",
        "session_already_exist" => "Session Already Exist",
        "conflict" => "Conflict",
        "refund_completed" => "Refund Completed",
        "error" => "Error",
        "parent_session_not_exists_or_it_has_been_successfully_aborted" => "Parent session doesn't exists or it has been successfully aborted",
        "session_with_this_id_already_exists_in_database" => "Session with this ID already exists in database",
        "session_id_is_required" => "Session id is required",
        "cash_register_id_is_required" => "Cash Register id is required",
        "transaction_id_is_required" => "Transaction id is required",
        "account_id_is_required" => "Account id is required",
        "merchantId_is_required" => "merchantId is required",
        "session_aborted" => "Session Aborted",
        "success_response" => "Successfull Response",
    ],

    'cannot_retry_payment' => "Can not retry this payment",
    "company_report_z_list_return" => "Company z report list return successfully",
    'current_batch_returned_successfully' => "Current batch returned successfully",
    "no_batch_open" => "No batch is open",
    "batch_closed_successfully" => "Batch closed successfully",
    "cannot_abort_this_refund_receipt" => "cannot abort this refund receipt",
    "start_date_and_end_date_required_for_generating_report" => "Start date and end date required for generating report",
    "quantity_more_than_available" => "Quantity more than available",

    "out_of_stock" => "We're sorry, but the item is currently out of stock",

    "gift_card" => [
        "gift_card" => "Gift Card",

        CompanyGiftCard::STATUS_ACTIVE => "Active",
        CompanyGiftCard::STATUS_INACTIVE => "In Active",
        CompanyGiftCard::STATUS_EXPIRED => "Expired",

        "company_s_gift_card" => ":company's Gift Card",
    ],

    "receipt" => [
        "status" => [
            CompanyReceipt::PAID => "Paid",
            CompanyReceipt::PARTIALLY_REFUNDED => "Partially Refunded",
            CompanyReceipt::ABORTED => "Aborted",
            CompanyReceipt::PENDING => "Pending",
            CompanyReceipt::PROCESSING => "Processing",
            CompanyReceipt::REFUNDED => "Refunded",
            CompanyReceipt::CANCELLED => "Cancelled",
        ],
    ],
    "ccu" => [
        "company_ccu_info_updated_successfully" => "Company ccu info updated successfully",
        "register_id_already_open" => "Register :registerId is already open",
        "success_response" => "Success",
        "error" => '',
        "company_ccu_enrollment_open_successfully" => "Company ccu enrollment open successfully",
        "register_id_already_closed" => "Register :registerId is already closed",
        "company_ccu_enrollment_close_successfully" => "Company ccu enrollment close successfully",
        "company_ccu_enroll_successfully" => "Company ccu enroll successfully",
        "company_already_enroll_in_ccu" => 'Company already enrolled in Infrasec',

        "organization_number_is_required" => "Organization number is required",
        "company_name_is_required" => "Company name is required",
        "street_address_is_required" => "Street address is required",
        "city_is_required" => "City is required",
        "zip_code_is_required" => "Zip code is required",
        "only_super_admin_can_add_merchant_id" => "only super admin can add merchant id",


    ],

    "company_name" => "Company name",
    "email" => "Email",
    "phone_number" => "Phone number",
    "organization_number" => "Organization number",
    "street_address" => "Street address",
    "city" => "City",
    "zip_code" => "Zip code",

    "refund_receipt_copy_can_be_download_only_once" => "Refund receipt copy can be download only once",
    "receipt_can_be_download_only_once" => "Receipt copy can be download only once",

    "receipt_items_returned_successfully" => "Receipt items returned successfully",
];
