<?php

return [

    /*
    |--------------------------------------------------------------------------
    | <PERSON><PERSON> CORS Options
    |--------------------------------------------------------------------------
    |
    | The allowed_methods and allowed_headers options are case-insensitive.
    |
    | You don't need to provide both allowed_origins and allowed_origins_patterns.
    | If one of the strings passed matches, it is considered a valid origin.
    |
    | If array('*') is provided to allowed_methods, allowed_origins or allowed_headers
    | all methods / origins / headers are allowed.
    |
    */

    /*
     * You can enable CORS for 1 or multiple paths.
     * Example: ['api/*']
     */
    'paths' => ['*'],

    /*
    * Matches the request method. `[*]` allows all methods.
    */
    'allowed_methods' => ['*'],

    /*
     * Matches the request origin. `[*]` allows all origins.
     */
    // 'allowed_origins' => ['*'],
    // 'allowed_origins' => ['http://localhost:3000', 'meridiq.netlify.app', 'https://meridiq.netlify.app', 'https://admin.meridiq.com', 'https://admin-test.meridiq.com', 'https://test1.meridiq.com', 'http://localhost:3001', 'https://localhost:3000', 'https://meridiq.kodemakers.dev', 'http://localhost:5000', 'https://app-test.meridiq.com', 'https://meridiq.com', 'http://meridiq-web.test', 'http://***********', 'https://meridiq-web.herokuapp.com', 'http://meridiq-web.herokuapp.com', "https://app.meridiq.com"],
    'allowed_origins' => config('app.env') == 'production' ? [
        'https://admin.meridiq.com',
        'https://admin-test.meridiq.com',
        'https://test1.meridiq.com',
        'https://app-test.meridiq.com',
        'https://meridiq.com',
        'https://app.meridiq.com',
        'https://main-pos.early-access.meridiq.com',
        'https://admin-test-pos.early-access.meridiq.com',
        'https://prod-v2-admin.early-access.meridiq.com',
        'https://prod-v2.early-access.meridiq.com',
        'http://app.meridiq.com',
    ] : ['*'],

    /*
     * Matches the request origin with, similar to `Request::is()`
     */
    'allowed_origins_patterns' => config('app.env') == 'production' ? [] : ['*'],

    /*
     * Sets the Access-Control-Allow-Headers response header. `[*]` allows all headers.
     */
    'allowed_headers' => ['*'],

    /*
     * Sets the Access-Control-Expose-Headers response header with these headers.
     */
    'exposed_headers' => [],

    /*
     * Sets the Access-Control-Max-Age response header when > 0.
     */
    'max_age' => env('CORS_MAX_AGE', 0),

    /*
     * Sets the Access-Control-Allow-Credentials header.
     */
    'supports_credentials' => true,
];
