<?php

use App\Models\GeneralTemplateQuestion;
use App\QuestionaryQuestion;

return [
    'type' => [
        QuestionaryQuestion::YES_NO,
        QuestionaryQuestion::YES_NO_TEXTBOX,
        QuestionaryQuestion::TEXTBOX,
        QuestionaryQuestion::FILE_UPLOAD,
        // QuestionaryQuestion::IMAGE,
        QuestionaryQuestion::SELECT,
        // QuestionaryQuestion::MULTI_SELECT,
        QuestionaryQuestion::RADIO,
        QuestionaryQuestion::CHECKBOX,
        QuestionaryQuestion::INPUT,
        QuestionaryQuestion::RANGE,
    ],

    'type_with_answer_validation' => [
        QuestionaryQuestion::YES_NO => [
            "required" => [
                "data._index" => "required",
                "data._index.value" => "in:yes,no|required",
            ],
            "normal" => [
                "data._index" => "nullable",
                "data._index.value" => "in:yes,no,null|nullable",
            ]
        ],
        QuestionaryQuestion::YES_NO_TEXTBOX => [
            "required" => [
                "data._index" => "required",
                "data._index.value" => "in:yes,no|required",
                "data._index.text" => "required_if:data._index.value,yes|max:1000",
            ],
            "normal" => [
                "data._index" => "required",
                "data._index.value" => "in:yes,no,null|nullable",
                "data._index.text" => "nullable|max:1000",
            ]
        ],
        QuestionaryQuestion::TEXTBOX => [
            "required" => [
                "data._index" => "max:1000|required",
            ],
            "normal" => [
                "data._index" => "max:1000|nullable",
            ]
        ],
        GeneralTemplateQuestion::HTML_EDITOR => [
            "required" => [
                "data._index.text" => "max:10000|required",
            ],
            "normal" => [
                "data._index.text" => "max:10000|nullable",
            ]
        ],
        QuestionaryQuestion::IMAGE => [
            "required" => [
                "data._index" => "image|required",
            ],
            "normal" => [
                "data._index" => "image|nullable",
            ]
        ],
        QuestionaryQuestion::SELECT => [
            "required" => [
                "data._index" => "in:_options|required",
            ],
            "normal" => [
                "data._index" => "in:_options|nullable",
            ],
        ],
        QuestionaryQuestion::MULTI_SELECT => [
            "required" => [
                "data._index" => "array|min:1|required",
                "data._index.*" => "in:_options|distint|required"
            ],
            "normal" => [
                "data._index" => "array",
                "data._index.*" => "in:_options|distint"
            ]
        ],
        QuestionaryQuestion::RADIO => [
            "required" => [
                "data._index" => "in:_options|required",
            ],
            "normal" => [
                "data._index" => "in:_options|nullable",
            ],
        ],
        QuestionaryQuestion::CHECKBOX => [
            "required" => [
                "data._index" => "array|min:1|required",
                "data._index.*" => "in:_options|distint|required"
            ],
            "normal" => [
                "data._index" => "array",
                "data._index.*" => "in:_options|distint"
            ]
        ],
        QuestionaryQuestion::INPUT => [
            "required" => [
                "data._index" => "max:500|required",
            ],
            "normal" => [
                "data._index" => "max:500|nullable",
            ]
        ],
        QuestionaryQuestion::RANGE => [
            "required" => [
                "data._index" => "integer|min:0|max:10|required",
            ],
            "normal" => [
                "data._index" => "integer|min:0|max:10|nullable",
            ]
        ],
    ],

    'type_with_options' => [
        QuestionaryQuestion::IMAGE => [
            "create" => [
                'required',
                'image',
            ],
            "update" => [
                'image',
            ],
        ],
        QuestionaryQuestion::SELECT => [
            "create" => [
                'required',
                'min:2',
                'array',
            ],
            "update" => [
                'required',
                'min:2',
                'array',
            ],
        ],
        QuestionaryQuestion::RADIO => [
            "create" => [
                'required',
                'min:2',
                'array',
            ],
            "update" => [
                'required',
                'min:2',
                'array',
            ],
        ],
        QuestionaryQuestion::MULTI_SELECT => [
            "create" => [
                'required',
                'min:2',
                'array',
            ],
            "update" => [
                'required',
                'min:2',
                'array',
            ],
        ],
        QuestionaryQuestion::CHECKBOX => [
            "create" => [
                'required',
                'min:2',
                'array',
            ],
            "update" => [
                'required',
                'min:2',
                'array',
            ],
        ],
        QuestionaryQuestion::RANGE => [
            "create" => [
                'required',
                'min:2',
                'array',
            ],
            "update" => [
                'required',
                'min:2',
                'array',
            ],
        ],
    ],

    'type_with_image' => [
        QuestionaryQuestion::IMAGE,
    ],
];
