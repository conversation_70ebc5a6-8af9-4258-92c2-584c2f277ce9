<?php

use App\CompanyPlatform;

$is_in_testing = env('APP_ENV') == 'testing';

if ($is_in_testing) {
    return [
        [
            "name" => "PRESCRIPTION",
            "stripe_ids" => [
                'prod_PD5uXLDUSgcHDR',
                'prod_PD0ccMbstdnK0G'
            ],
            "platform" => CompanyPlatform::PRESCRIPTION,
        ],
        [
            "name" => "Subscription-2022",
            "stripe_ids" => [
                'prod_Mtz2GLzDpe7Uy4',
                'prod_JNvOVEGNisIZNG',
                'prod_HTbnZRlhJ1cPL3',
                'prod_H2wRpluprNUDL5'
            ],
            "platform" => CompanyPlatform::RECORD_SYSTEM,
        ],
        [
            "name" => "SMS",
            "stripe_ids" => [
                'prod_PHDNKKD5eFNvO0',
                'prod_PHEFtPyfCmPTkV',
                'prod_PHEHETeqVMKYVE'
            ],
            "platform" => CompanyPlatform::SMS,
        ],
    ];
} else {
    return [
        [
            "name" => "PRESCRIPTION",
            "stripe_ids" => ['prod_PD6K2WjNbiO1Lq'],
            "platform" => CompanyPlatform::PRESCRIPTION,
        ],
        [
            "name" => "Subscription-2022",
            "stripe_ids" => [
                'prod_O0V2wlAGL6NsEX',
                'prod_N7W5MiWjJtykJ7',
                'prod_Myosnhmch9hm3U',
                'prod_HInwhhFAqx2fag'
            ],
            "platform" => CompanyPlatform::RECORD_SYSTEM,
        ],
        [
            "name" => "SMS",
            "stripe_ids" => [
                'prod_PWZubqmJJP0Tuv',
                'prod_PWZxVyAmShM77E',
                'prod_PWZxjUVxtkNrR8'
            ],
            "platform" => CompanyPlatform::SMS,
        ],
    ];
}