<?php

use App\CompanyPlatform;

$is_prod = in_array(config('app.env'), ['production']);

if (!$is_prod) {
    return [
        [
            'name' => 'PRESCRIPTION',
            'quantity' => 1,
            'prices' => [
                [
                    'stripe_id' => 'price_1ORC7eCBmBWVCuBdCGGVVJPQ',
                    'currency' => 'SEK',
                    'price' => '20',
                ],
                [
                    'stripe_id' => 'price_1ORC74CBmBWVCuBdU72Xn64x',
                    'currency' => 'SEK',
                    'price' => '30',
                ],
                [
                    'stripe_id' => 'price_1OOfigCBmBWVCuBdJkqEqcnx',
                    'currency' => 'SEK',
                    'price' => '40',
                ],
            ],
            'platform' => CompanyPlatform::PRESCRIPTION,
        ],
        [
            'name' => '100-SMS',
            'quantity' => 100,
            'prices' => [
                [
                    'stripe_id' => 'price_1OSfgJCBmBWVCuBdDsZzb2FP',
                    'currency' => 'EUR',
                    'price' => '10',
                ],
                [
                    'stripe_id' => 'price_1OSfV3CBmBWVCuBdgoFaEuXk',
                    'currency' => 'GBP',
                    'price' => '9',
                ],
                [
                    'stripe_id' => 'price_1OSfSBCBmBWVCuBdxI2YNSXF',
                    'currency' => 'USD',
                    'price' => '11',
                ],
                [
                    'stripe_id' => 'price_1OSewwCBmBWVCuBdywwlqjpS',
                    'currency' => 'SEK',
                    'price' => '120',
                ],
            ],
            'platform' => CompanyPlatform::SMS,
        ],
        [
            'name' => '1000-SMS',
            'quantity' => 1000,
            'prices' => [
                [
                    'stripe_id' => 'price_1OSfoYCBmBWVCuBdJLYBMjh0',
                    'currency' => 'EUR',
                    'price' => '90',
                ],
                [
                    'stripe_id' => 'price_1OSfoJCBmBWVCuBdIy9w6uTB',
                    'currency' => 'GBP',
                    'price' => '75',
                ],
                [
                    'stripe_id' => 'price_1OSfnvCBmBWVCuBdPiLs6Hs0',
                    'currency' => 'USD',
                    'price' => '100',
                ],
                [
                    'stripe_id' => 'price_1OSfn7CBmBWVCuBdzE76NhW4',
                    'currency' => 'SEK',
                    'price' => '1000',
                ],
            ],
            'platform' => CompanyPlatform::SMS,
        ],
        [
            'name' => '5000-SMS',
            'quantity' => 5000,
            'prices' => [
                [
                    'stripe_id' => 'price_1OSfuoCBmBWVCuBdYFx2RgxA',
                    'currency' => 'EUR',
                    'price' => '350',
                ],
                [
                    'stripe_id' => 'price_1OSfuGCBmBWVCuBdGymnCPiK',
                    'currency' => 'GBP',
                    'price' => '300',
                ],
                [
                    'stripe_id' => 'price_1OSfpYCBmBWVCuBdLwoj2pbF',
                    'currency' => 'USD',
                    'price' => '400',
                ],
                [
                    'stripe_id' => 'price_1OSfpFCBmBWVCuBdc3WP9hMv',
                    'currency' => 'SEK',
                    'price' => '4000',
                ],
            ],
            'platform' => CompanyPlatform::SMS,
        ],
        [
            'name' => "POS SYSTEM",
            'quantity' => 1,
            'prices' => [
                [
                    'stripe_id' => 'price_1OvcowCBmBWVCuBdnrLfdbex',
                    'currency' => 'SEK',
                    'price' => '199',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::POS_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1PqtpPCBmBWVCuBdHnErfcMQ',
                    'currency' => 'EUR',
                    'price' => '19',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::POS_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
            ],
            'yearly_prices' => [
                [
                    'stripe_id' => 'price_1OvcqbCBmBWVCuBdTowCcJjD',
                    'currency' => 'SEK',
                    'price' => '2388',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::POS_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1PqtrECBmBWVCuBdli7kUnEr',
                    'currency' => 'EUR',
                    'price' => '228',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::POS_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
            ],
            'platform' => CompanyPlatform::POS_SYSTEM,
            'product_id' => "prod_Pl97HxksuAU08s",
        ],
        [
            'name' => "RECORD SYSTEM",
            'quantity' => 1,
            'prices' => [
                [
                    'stripe_id' => 'price_1MATQGCBmBWVCuBdwd5iNYR4',
                    'currency' => 'EUR',
                    'max_quantity' => 15,
                    'price' => '24',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1MABLmCBmBWVCuBd5ZciHYor',
                    'currency' => 'GBP',
                    'max_quantity' => 15,
                    'price' => '22',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1MABIACBmBWVCuBdj2sQuNXZ',
                    'currency' => 'USD',
                    'max_quantity' => 15,
                    'price' => '26',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1MABKoCBmBWVCuBdETKp49rW',
                    'currency' => 'SEK',
                    'max_quantity' => 15,
                    'price' => '249',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],




                // Very old plans
                [
                    'stripe_id' => 'plan_HInwLF7p1lgjk2',
                    'currency' => 'EUR',
                    'max_quantity' => 15,
                    'price' => '0',
                    'is_free' => true,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'price_1It9oDCBmBWVCuBdxioStgMG',
                    'currency' => 'GBP',
                    'max_quantity' => 15,
                    'price' => '0',
                    'is_free' => true,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'price_1It9nLCBmBWVCuBdx3tuZ4ka',
                    'currency' => 'USD',
                    'max_quantity' => 15,
                    'price' => '0',
                    'is_free' => true,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'price_1It9msCBmBWVCuBdlfNmXtgF',
                    'currency' => 'SEK',
                    'max_quantity' => 15,
                    'price' => '0',
                    'is_free' => true,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'price_1IlYRUCBmBWVCuBdBsmscuFH',
                    'currency' => 'EUR',
                    'max_quantity' => 15,
                    'price' => '17',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'price_1It8Z0CBmBWVCuBdRPatw13P',
                    'currency' => 'USD',
                    'max_quantity' => 15,
                    'price' => '17',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'price_1It8ZfCBmBWVCuBdJq1WThzh',
                    'currency' => 'GBP',
                    'max_quantity' => 15,
                    'price' => '12',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'price_1It8aFCBmBWVCuBdpS0WqGFe',
                    'currency' => 'SEK',
                    'max_quantity' => 15,
                    'price' => '140',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'plan_HInwPAWQvXHRBc',
                    'currency' => 'EUR',
                    'max_quantity' => 15,
                    'price' => '5',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'plan_HInwnnf5eqtMLB',
                    'currency' => 'EUR',
                    'max_quantity' => 15,
                    'price' => '25',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
                [
                    'stripe_id' => 'plan_HInwfnuKz8QiE2',
                    'currency' => 'EUR',
                    'max_quantity' => 15,
                    'price' => '59',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => false,
                    'depricated' => true,
                ],
            ],
            'yearly_prices' => [
                [
                    'stripe_id' => 'price_1MtmcvCBmBWVCuBdiBiUtfhv',
                    'currency' => 'EUR',
                    'max_quantity' => 15,
                    'price' => '230',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => true,
                    'depricated' => false,
                ],
                [
                    'stripe_id' => 'price_1MtmYZCBmBWVCuBdVY7mX1LR',
                    'currency' => 'GBP',
                    'max_quantity' => 15,
                    'price' => '211',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => true,
                    'depricated' => false,
                ],
                [
                    'stripe_id' => 'price_1Mtma4CBmBWVCuBdtnwqH9NF',
                    'currency' => 'USD',
                    'max_quantity' => 15,
                    'price' => '250',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => true,
                    'depricated' => false,
                ],
                [
                    'stripe_id' => 'price_1MtmW2CBmBWVCuBd6TMu6VjN',
                    'currency' => 'SEK',
                    'max_quantity' => 15,
                    'price' => '2390',
                    'is_free' => false,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => true,
                    'depricated' => false,
                ],
                [
                    'stripe_id' => 'price_1MAB4sCBmBWVCuBdM02k4pRx',
                    'currency' => 'SEK',
                    'price' => '0',
                    'is_free' => true,
                    'max_quantity' => 1,
                    'depricated' => true,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1MABChCBmBWVCuBdoAbcElZe',
                    'currency' => 'GBP',
                    'price' => '0',
                    'is_free' => true,
                    'max_quantity' => 1,
                    'depricated' => true,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1MABDGCBmBWVCuBddFg8KfZf',
                    'currency' => 'USD',
                    'price' => '0',
                    'max_quantity' => 1,
                    'is_free' => true,
                    'depricated' => true,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1MABDvCBmBWVCuBd5BmaHyPP',
                    'currency' => 'EUR',
                    'price' => '0',
                    'max_quantity' => 1,
                    'is_free' => true,
                    'depricated' => true,
                    'users' => 1,
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'is_yearly' => true,
                ],
            ],
            'users' => 1,
            'platform' => CompanyPlatform::RECORD_SYSTEM,
            'product_id' => "prod_Mtz2GLzDpe7Uy4",
        ],
        [
            'name' => "QUALITY MANAGEMENT SYSTEM",
            'quantity' => 1,
            'prices' => [
                [
                    'stripe_id' => 'price_1QNuqoCBmBWVCuBdzqphJ0af',
                    'currency' => 'EUR',
                    'price' => '5',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1QNv8nCBmBWVCuBdbt0CKhjQ',
                    'currency' => 'GBP',
                    'price' => '4',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1QNtUKCBmBWVCuBdTPwPKdyx',
                    'currency' => 'SEK',
                    'price' => '49',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1QOySiCBmBWVCuBdn8nAnrHt',
                    'currency' => 'USD',
                    'price' => '5',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1QPHrYCBmBWVCuBdUXJdhdCO',
                    'currency' => 'USD',
                    'price' => '350',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1QNvPZCBmBWVCuBdPYA4KgGW',
                    'currency' => 'EUR',
                    'price' => '350',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1QNvQSCBmBWVCuBdysvZMc0r',
                    'currency' => 'GBP',
                    'price' => '290',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1QNvOXCBmBWVCuBdJtPpnlsH',
                    'currency' => 'SEK',
                    'price' => '3990',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
            ],
            'yearly_prices' => [
                [
                    'stripe_id' => 'price_1QNvA9CBmBWVCuBd5uLmyRhV',
                    'currency' => 'SEK',
                    'price' => '470',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1QNvApCBmBWVCuBdeY1A2RDS',
                    'currency' => 'GBP',
                    'price' => '40',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1QNvKXCBmBWVCuBdGu86ZrUN',
                    'currency' => 'USD',
                    'price' => '48',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1QNvDkCBmBWVCuBdd0l6FMJ9',
                    'currency' => 'EUR',
                    'price' => '48',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1QPHrYCBmBWVCuBdUXJdhdCO',
                    'currency' => 'USD',
                    'price' => '350',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1QNvPZCBmBWVCuBdPYA4KgGW',
                    'currency' => 'EUR',
                    'price' => '350',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1QNvQSCBmBWVCuBdysvZMc0r',
                    'currency' => 'GBP',
                    'price' => '290',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
                [
                    'stripe_id' => 'price_1QNvOXCBmBWVCuBdJtPpnlsH',
                    'currency' => 'SEK',
                    'price' => '3990',
                    'is_free' => false,
                    'max_quantity' => 1,
                    'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                    'depricated' => false,
                    'is_yearly' => true,
                ],
            ],
            'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
            'product_id' => "prod_RFFh8emGEyt5s0",
        ],
        [
            'name' => "BOOKING SYSTEM",
            'quantity' => 1,
            'prices' => [
                [
                    'stripe_id' => 'price_1QUQAfCBmBWVCuBdjNWGXgb5',
                    'currency' => 'EUR',
                    'price' => '5',
                    'is_free' => false,
                    'max_quantity' => 15,
                    'users' => 1,
                    'platform' => CompanyPlatform::BOOKING_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                ],
                [
                    'stripe_id' => 'price_1QUQB8CBmBWVCuBd4ObSWrdq',
                    'currency' => 'GBP',
                    'price' => '4',
                    'is_free' => false,
                    'max_quantity' => 15,
                    'platform' => CompanyPlatform::BOOKING_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                    'users' => 1,
                ],
                [
                    'stripe_id' => 'price_1QUQ8QCBmBWVCuBdBLyddCmB',
                    'currency' => 'SEK',
                    'price' => '49',
                    'is_free' => false,
                    'max_quantity' => 15,
                    'platform' => CompanyPlatform::BOOKING_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                    'users' => 1,
                ],
                [
                    'stripe_id' => 'price_1QUQA7CBmBWVCuBdr0fv6qwP',
                    'currency' => 'USD',
                    'price' => '5',
                    'is_free' => false,
                    'max_quantity' => 15,
                    'platform' => CompanyPlatform::BOOKING_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => false,
                    'users' => 1,
                ],
            ],
            'yearly_prices' => [
                [
                    'stripe_id' => 'price_1QUQF9CBmBWVCuBdOpkfAkx8',
                    'currency' => 'SEK',
                    'price' => '470',
                    'is_free' => false,
                    'max_quantity' => 15,
                    'platform' => CompanyPlatform::BOOKING_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                    'users' => 1,
                ],
                [
                    'stripe_id' => 'price_1QUQGfCBmBWVCuBdJ83Ar3Ox',
                    'currency' => 'GBP',
                    'price' => '40',
                    'is_free' => false,
                    'max_quantity' => 15,
                    'platform' => CompanyPlatform::BOOKING_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                    'users' => 1,
                ],
                [
                    'stripe_id' => 'price_1QUQGCCBmBWVCuBdOkKa7ldX',
                    'currency' => 'USD',
                    'price' => '48',
                    'is_free' => false,
                    'max_quantity' => 15,
                    'platform' => CompanyPlatform::BOOKING_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                    'users' => 1,
                ],
                [
                    'stripe_id' => 'price_1QUQFnCBmBWVCuBdZyZaHlfe',
                    'currency' => 'EUR',
                    'price' => '48',
                    'is_free' => false,
                    'max_quantity' => 15,
                    'platform' => CompanyPlatform::BOOKING_SYSTEM,
                    'depricated' => false,
                    'is_yearly' => true,
                    'users' => 1,
                ],
            ],
            'users' => 1,
            'platform' => CompanyPlatform::BOOKING_SYSTEM,
            'product_id' => "prod_RNATXHXWpI2gbW",
        ],
    ];
}


return [
    [
        "name" => "Ordination",
        'quantity' => 1,
        "prices" => [
            [
                "stripe_id" => 'price_1OSI13CBmBWVCuBdvOxzBq9F',
                "price" => '20',
            ],
            [
                "stripe_id" => 'price_1OSI1TCBmBWVCuBdSJStBUhy',
                "price" => '30',
            ],
            [
                "stripe_id" => 'price_1OOg7tCBmBWVCuBdKL4ZLudr',
                "price" => '40',
            ],
        ],
        "platform" => CompanyPlatform::PRESCRIPTION,
    ],
    [
        'name' => '100-SMS',
        'quantity' => 100,
        'prices' => [
            [
                'stripe_id' => 'price_1OhWkJCBmBWVCuBdHQ8fGtfM',
                'currency' => 'EUR',
                'price' => '10',
            ],
            [
                'stripe_id' => 'price_1OhWkJCBmBWVCuBdAF5AJZvP',
                'currency' => 'GBP',
                'price' => '9',
            ],
            [
                'stripe_id' => 'price_1OhWkJCBmBWVCuBdinFWvXAj',
                'currency' => 'USD',
                'price' => '11',
            ],
            [
                'stripe_id' => 'price_1OhWkJCBmBWVCuBd1d0G7Hrf',
                'currency' => 'SEK',
                'price' => '120',
            ],
        ],
        'platform' => CompanyPlatform::SMS,
    ],
    [
        'name' => '1000-SMS',
        'quantity' => 1000,
        'prices' => [
            [
                'stripe_id' => 'price_1OhWnMCBmBWVCuBdpoaN4v5A',
                'currency' => 'EUR',
                'price' => '90',
            ],
            [
                'stripe_id' => 'price_1OhWnMCBmBWVCuBdYLKwVSI2',
                'currency' => 'GBP',
                'price' => '75',
            ],
            [
                'stripe_id' => 'price_1OhWnMCBmBWVCuBdzx9Exn6U',
                'currency' => 'USD',
                'price' => '100',
            ],
            [
                'stripe_id' => 'price_1OhWnMCBmBWVCuBdcSWRokKt',
                'currency' => 'SEK',
                'price' => '1000',
            ],
        ],
        'platform' => CompanyPlatform::SMS,
    ],
    [
        'name' => '5000-SMS',
        'quantity' => 5000,
        'prices' => [
            [
                'stripe_id' => 'price_1OhWo2CBmBWVCuBdZC2JDKzD',
                'currency' => 'EUR',
                'price' => '350',
            ],
            [
                'stripe_id' => 'price_1OhWo2CBmBWVCuBdbHDS7EKw',
                'currency' => 'GBP',
                'price' => '300',
            ],
            [
                'stripe_id' => 'price_1OhWo2CBmBWVCuBdQkj8LSZG',
                'currency' => 'USD',
                'price' => '400',
            ],
            [
                'stripe_id' => 'price_1OhWo2CBmBWVCuBdOYm37T4t',
                'currency' => 'SEK',
                'price' => '4000',
            ],
        ],
        'platform' => CompanyPlatform::SMS,
    ],


    //PRODUCTION PLANS
    [
        'name' => "POS SYSTEM",
        'quantity' => 1,
        'prices' => [
            [
                'stripe_id' => 'price_1P7ErvCBmBWVCuBdoaZtfQXd',
                'currency' => 'SEK',
                'price' => '199',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::POS_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1Prw55CBmBWVCuBdT11sXCEv',
                'currency' => 'EUR',
                'price' => '19',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::POS_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],
        ],
        'yearly_prices' => [
            [
                'stripe_id' => 'price_1P7ErvCBmBWVCuBdorVctY0l',
                'currency' => 'SEK',
                'price' => '2388',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::POS_SYSTEM,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1Prw6eCBmBWVCuBdfYBrnYxb',
                'currency' => 'EUR',
                'price' => '228',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::POS_SYSTEM,
                'depricated' => false,
                'is_yearly' => true,
            ],
        ],
        'product_id' => "prod_Px9AnCWUqNWjrg",
        'platform' => CompanyPlatform::POS_SYSTEM,
    ],
    [
        'name' => "RECORD SYSTEM",
        'quantity' => 1,
        'prices' => [
            [
                'stripe_id' => 'price_1MErJACBmBWVCuBd193MrjzP',
                'currency' => 'EUR',
                'max_quantity' => 15,
                'price' => '24',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1MErHxCBmBWVCuBdtZyTFH6D',
                'currency' => 'GBP',
                'max_quantity' => 15,
                'price' => '22',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1MErIdCBmBWVCuBdfoub9Irj',
                'currency' => 'USD',
                'max_quantity' => 15,
                'price' => '26',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1MErHOCBmBWVCuBduSGHskBf',
                'currency' => 'SEK',
                'max_quantity' => 15,
                'price' => '249',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],




            // Very old plans
            [
                'stripe_id' => 'plan_HInwLF7p1lgjk2',
                'currency' => 'EUR',
                'max_quantity' => 15,
                'price' => '0',
                'is_free' => true,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'price_1It9oDCBmBWVCuBdxioStgMG',
                'currency' => 'GBP',
                'max_quantity' => 15,
                'price' => '0',
                'is_free' => true,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'price_1It9nLCBmBWVCuBdx3tuZ4ka',
                'currency' => 'USD',
                'max_quantity' => 15,
                'price' => '0',
                'is_free' => true,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'price_1It9msCBmBWVCuBdlfNmXtgF',
                'currency' => 'SEK',
                'max_quantity' => 15,
                'price' => '0',
                'is_free' => true,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'price_1IlYRUCBmBWVCuBdBsmscuFH',
                'currency' => 'EUR',
                'max_quantity' => 15,
                'price' => '17',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'price_1It8Z0CBmBWVCuBdRPatw13P',
                'currency' => 'USD',
                'max_quantity' => 15,
                'price' => '17',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'price_1It8ZfCBmBWVCuBdJq1WThzh',
                'currency' => 'GBP',
                'max_quantity' => 15,
                'price' => '12',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'price_1It8aFCBmBWVCuBdpS0WqGFe',
                'currency' => 'SEK',
                'max_quantity' => 15,
                'price' => '140',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'plan_HInwPAWQvXHRBc',
                'currency' => 'EUR',
                'max_quantity' => 15,
                'price' => '5',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'plan_HInwnnf5eqtMLB',
                'currency' => 'EUR',
                'max_quantity' => 15,
                'price' => '25',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],
            [
                'stripe_id' => 'plan_HInwfnuKz8QiE2',
                'currency' => 'EUR',
                'max_quantity' => 15,
                'price' => '59',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => false,
                'depricated' => true,
            ],

            //
        ],
        'yearly_prices' => [
            [
                'stripe_id' => 'price_1N5SVoCBmBWVCuBd3bQAhxRZ',
                'currency' => 'EUR',
                'max_quantity' => 15,
                'price' => '230',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => true,
                'depricated' => false,
            ],
            [
                'stripe_id' => 'price_1N5SUGCBmBWVCuBd01qyCBh8',
                'currency' => 'GBP',
                'max_quantity' => 15,
                'price' => '211',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => true,
                'depricated' => false,
            ],
            [
                'stripe_id' => 'price_1N5SUfCBmBWVCuBdFBAKevfy',
                'currency' => 'USD',
                'max_quantity' => 15,
                'price' => '250',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => true,
                'depricated' => false,
            ],
            [
                'stripe_id' => 'price_1N5STuCBmBWVCuBdjsDVdbqD',
                'currency' => 'SEK',
                'max_quantity' => 15,
                'price' => '2390',
                'is_free' => false,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => true,
                'depricated' => false,
            ],

            // Free Plans
            [
                'stripe_id' => 'price_1MErEzCBmBWVCuBdbSu1griI',
                'currency' => 'SEK',
                'price' => '0',
                'is_free' => true,
                'max_quantity' => 1,
                'depricated' => true,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1MErFlCBmBWVCuBdAxbSqw6r',
                'currency' => 'GBP',
                'price' => '0',
                'is_free' => true,
                'max_quantity' => 1,
                'depricated' => true,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1MErGDCBmBWVCuBdH7JFHdmm',
                'currency' => 'USD',
                'price' => '0',
                'max_quantity' => 1,
                'is_free' => true,
                'depricated' => true,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1MErGpCBmBWVCuBd8eSwFFV1',
                'currency' => 'EUR',
                'price' => '0',
                'max_quantity' => 1,
                'is_free' => true,
                'depricated' => true,
                'users' => 1,
                'platform' => CompanyPlatform::RECORD_SYSTEM,
                'is_yearly' => true,
            ],
        ],
        'users' => 1,
        'product_id' => "prod_Myosnhmch9hm3U",
        'platform' => CompanyPlatform::RECORD_SYSTEM,
    ],
    [
        'name' => "QUALITY MANAGEMENT SYSTEM",
        'quantity' => 1,
        'prices' => [
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBda3MooGDE',
                'currency' => 'EUR',
                'price' => '5',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBddbUkOZVd',
                'currency' => 'GBP',
                'price' => '4',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdBmp9Bnjr',
                'currency' => 'SEK',
                'price' => '49',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdMrYo0etc',
                'currency' => 'USD',
                'price' => '5',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBd2bP9Wbxu',
                'currency' => 'USD',
                'price' => '350',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBd5vKjS0rM',
                'currency' => 'EUR',
                'price' => '350',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdwkNxgSYx',
                'currency' => 'GBP',
                'price' => '290',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdam2849Pf',
                'currency' => 'SEK',
                'price' => '3990',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                'depricated' => false,
                'is_yearly' => false,
            ],
        ],
        'yearly_prices' => [
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdDhjWIn4r',
                'currency' => 'SEK',
                'price' => '470',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdqAqLU2dd',
                'currency' => 'GBP',
                'price' => '40',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdF9F6KR1G',
                'currency' => 'USD',
                'price' => '48',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdfvTDVP0i',
                'currency' => 'EUR',
                'price' => '48',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBd2bP9Wbxu',
                'currency' => 'USD',
                'price' => '350',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBd5vKjS0rM',
                'currency' => 'EUR',
                'price' => '350',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdwkNxgSYx',
                'currency' => 'GBP',
                'price' => '290',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QVBdcCBmBWVCuBdam2849Pf',
                'currency' => 'SEK',
                'price' => '3990',
                'is_free' => false,
                'max_quantity' => 1,
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_ACTIVATION,
                'depricated' => false,
                'is_yearly' => true,
            ],
        ],
        'product_id' => "prod_RNxYMO9814Q8Mz",
        'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
    ],
    [
        'name' => "BOOKING SYSTEM",
        'quantity' => 1,
        'prices' => [
            [
                'stripe_id' => 'price_1QXFILCBmBWVCuBdS6fApkit',
                'currency' => 'EUR',
                'price' => '5',
                'is_free' => false,
                'max_quantity' => 15,
                'platform' => CompanyPlatform::BOOKING_SYSTEM,
                'users' => 1,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QXFILCBmBWVCuBdRcasirQ4',
                'currency' => 'GBP',
                'price' => '4',
                'is_free' => false,
                'max_quantity' => 15,
                'platform' => CompanyPlatform::BOOKING_SYSTEM,
                'users' => 1,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QXFILCBmBWVCuBdVq6w5GKc',
                'currency' => 'SEK',
                'price' => '49',
                'is_free' => false,
                'max_quantity' => 15,
                'platform' => CompanyPlatform::BOOKING_SYSTEM,
                'users' => 1,
                'depricated' => false,
                'is_yearly' => false,
            ],
            [
                'stripe_id' => 'price_1QXFILCBmBWVCuBdNjEdexW9',
                'currency' => 'USD',
                'price' => '5',
                'is_free' => false,
                'max_quantity' => 15,
                'platform' => CompanyPlatform::BOOKING_SYSTEM,
                'users' => 1,
                'depricated' => false,
                'is_yearly' => false,
            ],
        ],
        'yearly_prices' => [
            [
                'stripe_id' => 'price_1QXFILCBmBWVCuBdgrTSBNuM',
                'currency' => 'SEK',
                'price' => '470',
                'is_free' => false,
                'max_quantity' => 15,
                'platform' => CompanyPlatform::BOOKING_SYSTEM,
                'users' => 1,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QXFILCBmBWVCuBdRUjtBrcp',
                'currency' => 'GBP',
                'price' => '40',
                'is_free' => false,
                'max_quantity' => 15,
                'platform' => CompanyPlatform::BOOKING_SYSTEM,
                'users' => 1,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QXFILCBmBWVCuBdCQ3lZw0j',
                'currency' => 'USD',
                'price' => '48',
                'is_free' => false,
                'max_quantity' => 15,
                'platform' => CompanyPlatform::BOOKING_SYSTEM,
                'users' => 1,
                'depricated' => false,
                'is_yearly' => true,
            ],
            [
                'stripe_id' => 'price_1QXFILCBmBWVCuBd03kHawn9',
                'currency' => 'EUR',
                'price' => '48',
                'is_free' => false,
                'max_quantity' => 15,
                'platform' => CompanyPlatform::BOOKING_SYSTEM,
                'users' => 1,
                'depricated' => false,
                'is_yearly' => true,
            ],
        ],
        'users' => 1,
        'product_id' => "prod_RQ5TUdCfAJGrR5",
        'platform' => CompanyPlatform::BOOKING_SYSTEM,
    ],
];
