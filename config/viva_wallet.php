<?php

use App\CompanyReceipt;

return [

    /*
    |--------------------------------------------------------------------------
    | VIVA_ENV
    |--------------------------------------------------------------------------
    |
    | environment for viva-wallet
    | possible values: demo, production
    |
    */
    'environment' => secret_env('VIVA_ENV', 'demo'),

    /*
    |--------------------------------------------------------------------------
    | URL
    |--------------------------------------------------------------------------
    |
    | The url/api for account/basicAuth
    | doc: https://developer.vivawallet.com/integration-reference/basic-auth/#step-2-make-api-calls-using-your-credentials
    |
    |
    */
    'url' => env('VIVA_URL', 'https://demo.vivapayments.com'),

    /*
    |--------------------------------------------------------------------------
    | ACCOUNT URL
    |--------------------------------------------------------------------------
    |
    | The url/api for account/clientCredentials
    | doc: https://developer.vivawallet.com/integration-reference/oauth2-authentication/#step-2-request-access-token
    |
    |
    */
    'account_url' => env('VIVA_ACCOUNT_URL', 'https://demo-accounts.vivapayments.com'),

    /*
    |--------------------------------------------------------------------------
    | API URL
    |--------------------------------------------------------------------------
    |
    | The url/api for API calls
    | doc: https://developer.vivawallet.com/apis-for-payments/payment-api/#section/SandBox
    |
    |
    */
    'api_url' => env('VIVA_API_URL', 'https://demo-api.vivapayments.com'),


    /*
    |--------------------------------------------------------------------------
    | Merchant Id
    |--------------------------------------------------------------------------
    |
    | Primarily used for APIs which authenticate with Basic Auth, as well as our WooCommerce Smart, OpenCart & Magento plugins.
    | doc: https://developer.vivawallet.com/getting-started/find-your-account-credentials/isv-credentials/
    |
    |
    */
    'merchant_id' => secret_env('VIVA_MERCHANT_ID', ''),

    /*
    |--------------------------------------------------------------------------
    | Merchant Id
    |--------------------------------------------------------------------------
    |
    | Primarily used for APIs which authenticate with Basic Auth, as well as our WooCommerce Smart, OpenCart & Magento plugins.
    | doc: https://developer.vivawallet.com/getting-started/find-your-account-credentials/isv-credentials/
    |
    |
    */
    'reseller_id' => secret_env('VIVA_RESELLER_ID', ''),

    /*
    |--------------------------------------------------------------------------
    | Api Key
    |--------------------------------------------------------------------------
    |
    | Primarily used for APIs which authenticate with Basic Auth, as well as our WooCommerce Smart, OpenCart & Magento plugins.
    | doc: https://developer.vivawallet.com/getting-started/find-your-account-credentials/isv-credentials/
    |
    |
    */
    'api_key' => secret_env('VIVA_API_KEY', ''),


    /*
    |--------------------------------------------------------------------------
    | Client Id
    |--------------------------------------------------------------------------
    |
    | client_id for fetching the AUTH token
    | doc: https://developer.vivawallet.com/getting-started/find-your-account-credentials/isv-credentials/
    |
    |
    */
    'client_id' => secret_env('VIVA_CLIENT_ID', 'sample.apps.vivapayments.com'),

    /*
    |--------------------------------------------------------------------------
    | Client Secret
    |--------------------------------------------------------------------------
    |
    | client_secret for fetching the AUTH token
    | doc: https://developer.vivawallet.com/getting-started/find-your-account-credentials/isv-credentials/
    |
    |
    */
    'client_secret' => secret_env('VIVA_CLIENT_SECRET', ''),


    /*
    |--------------------------------------------------------------------------
    | return_url
    |--------------------------------------------------------------------------
    |
    | Return URL to send with API
    | doc: https://developer.vivawallet.com/isv-partner-program/payment-isv-api/#tag/Connected-Accounts/paths/~1isv~1v1~1accounts/post
    |
    |
    */
    'return_url' => secret_env('VIVA_RETURN_URL', "https://example.com/connected"),


    /*
    |--------------------------------------------------------------------------
    | maximum_amount
    |--------------------------------------------------------------------------
    |
    | Maximum amount for viva payable amount
    |
    |
    */
    'maximum_amount' => env('VIVA_MAXIMUM_AMOUNT', 999999.99),

    /*
    |--------------------------------------------------------------------------
    | isv_percentage
    |--------------------------------------------------------------------------
    |
    | ISV percentage of total amount
    |
    |
    */
    'isv_percentage' => env('VIVA_ISV_PERCENTAGE', 0.0095),


    /*
    |--------------------------------------------------------------------------
    | pan_entry_mode
    |--------------------------------------------------------------------------
    |
    | List of PAN entry modes and their meanings.
    |
    |
    */
    "pan_entry_mode" => [
        '00' => 'Unknown',
        '01' => 'Manual',
        '02' => 'Magnetic stripe',
        '03' => 'Bar code',
        '04' => 'OCR',
        '05' => 'Integrated circuit card (ICC). CVV can be checked.',
        '07' => 'Auto entry via contactless EMV',
        '09' => 'PAN entry via electronic commerce, including remote chip',
        '10' => 'Merchant has Cardholder Credentials on File',
        '79' => "Chip card at a chip capable terminal was unable to process transaction using data on the chip or magnetic strip-PAN entry via manual entry",
        '80' => 'Fallback from integrated circuit card (ICC) to magnetic stripe',
        '81' => 'PAN entry via electronic commerce, including chip',
        '82' => 'PAN auto entry via Server (issuer, acquirer, or third-party vendor system)',
        '90' => 'Magnetic stripe as read from track 2. CVV can be checked.',
        '91' => 'Auto entry via contactless magnetic stripe',
        '95' => 'Integrated circuit card (ICC). CVV may not be checked.',
        '99' => 'Same as original transaction',
    ],

    /*
    |--------------------------------------------------------------------------
    | viva_email
    |--------------------------------------------------------------------------
    |
    | Viva mail to send client data for verification
    |
    |
    */
    'viva_email' => env('VIVA_EMAIL', ''),

    /*
    |--------------------------------------------------------------------------
    | Transaction type
    |--------------------------------------------------------------------------
    |
    | The transaction type is identified by the TransactionTypeId parameter returned in the response body. It applies to the following:
    |
    |
    */
    'transaction_type' => [
        "0" =>    "Card capture",
        "1" =>    "Card pre-auth",
        "4" =>    "Card refund",
        "5" =>    "Card charge",
        "6" =>    "Card charge (installments)",
        "7" =>    "Card void",
        "8" =>    "Card Original Credit (refund, betting MCC only)",
        "9" =>    "Viva Wallet charge",
        "11" =>    "Viva Wallet refund",
        "13" =>    "Card refund claimed",
        "15" =>    "Dias",
        "16" =>    "Cash",
        "17" =>    "Cash refund",
        "18" =>    "Card refund (installments)",
        "19" =>    "Card payout",
        "20" =>    "Alipay charge",
        "21" =>    "Alipay refund",
        "22" =>    "Card manual cash disbursement",
        "23" =>    "iDEAL charge",
        "24" =>    "iDEAL refund",
        "25" =>    "P24 charge",
        "26" =>    "P24 refund",
        "27" =>    "BLIK charge",
        "28" =>    "BLIK refund",
        "29" =>    "PayU charge",
        "30" =>    "PayU refund",
        "31" =>    "Card withdrawal",
        "36" =>    "Sofort charge",
        "37" =>    "Sofort refund",
        "38" =>    "EPS charge",
        "39" =>    "EPS refund",
        "40" =>    "WeChat Pay charge",
        "41" =>    "WeChat Pay refund",
        "42" =>    "BitPay charge",
        "48" =>    "PayPal charge",
        "49" =>    "PayPal refund",
        "50" =>    "Trustly charge",
        "51" =>    "Trustly refund",
        "52" =>    "Klarna charge",
        "53" =>    "Klarna refund",
        "54" =>    "MB WAY charge",
        "55" =>    "MB WAY refund",
        "56" =>    "Multibanco charge",
        "57" =>    "Multibanco refund",
        "58" =>    "Payconiq charge",
        "59" =>    "Payconiq refund",
        "60" =>    "IRIS charge",
        "61" =>    "IRIS refund",
        "62" =>    "Pay By Bank charge",
        "63" =>    "Pay By Bank refund",
        "64" =>    "BANCOMAT Pay charge",
        "65" =>    "BANCOMAT Pay refund",
        "66" =>    "tbi bank charge",
        "67" =>    "tbi bank refund",
        "68" =>    "Pay on Delivery charge",
        "69" =>    "Card Verification",
        "70" =>    "Swish charge",
        "71" =>    "Swish refund",
        "74" =>    "Bluecode charge",
        "75" =>    "Bluecode refund",
        "78" =>    "Satispay charge",
        "79" =>    "Satispay refund",
        "80" =>    "Klarna pre-auth",
        "81" =>    "Klarna capture",
    ],
];
