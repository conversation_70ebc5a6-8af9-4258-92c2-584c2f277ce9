<?php

namespace App;

use App\Questionary;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Model;
use \AlexCrawford\Sortable\SortableTrait;

class QuestionaryQuestion extends Model
{
    use GetEncryptedFile, SortableTrait;
    const
        YES_NO = "yes_no",
        YES_NO_TEXTBOX = "yes_no_textbox",
        TEXTBOX = "textbox",
        RADIO = 'radio',
        CHECKBOX = 'checkbox',
        INPUT = 'input',
        RANGE = 'range',
        SELECT = 'select',
        MULTI_SELECT = 'multi_select',
        IMAGE = 'image',
        FILE_UPLOAD  = 'file_upload';

    protected $fillable = [
        'questionary_id',
        'question',
        'options',
        'required',
        'type',
        'order'
    ];

    protected $casts = [
        'required' => 'boolean',
        'options' => 'array',
    ];

    protected static $sortableGroupField = 'questionary_id';

    protected static $sortableField = 'order';

    public function questionary()
    {
        return $this->belongsTo(Questionary::class);
    }
}
