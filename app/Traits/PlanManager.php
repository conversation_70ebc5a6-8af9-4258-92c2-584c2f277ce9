<?php

namespace App\Traits;

use App\Company;
use App\CompanyBooking;
use App\CompanyBusinessHour;
use App\Mail\NewAccountUserMail;
use App\Plan;
use App\UserDevice;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use Throwable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Laravel\Cashier\Exceptions\IncompletePayment;

trait PlanManager
{
    static function updateNewPlanForThisCompany(Company $company, Request $request = null)
    {
        $plan_map = [
            //FREE PLAN
            [
                "old_plan_id" => "price_1It9oDCBmBWVCuBdxioStgMG", //GBP
                "old_plan_value" => "0.00",
                "new_plan_id" => "price_1MErFlCBmBWVCuBdAxbSqw6r", //GBP
                "new_plan_value" => "0.00",
            ],
            [
                "old_plan_id" => "price_1It9nLCBmBWVCuBdx3tuZ4ka", //USD
                "old_plan_value" => "0.00",
                "new_plan_id" => "price_1MErGDCBmBWVCuBdH7JFHdmm", //USD
                "new_plan_value" => "0.00",
            ],
            [
                "old_plan_id" => "price_1It9msCBmBWVCuBdlfNmXtgF", //SEK
                "old_plan_value" => "0.00",
                "new_plan_id" => "price_1MErEzCBmBWVCuBdbSu1griI", //SEK
                "new_plan_value" => "0.00",
            ],
            [
                "old_plan_id" => "plan_HInwLF7p1lgjk2", //EUR
                "old_plan_value" => "0.00",
                "new_plan_id" => "price_1MErGpCBmBWVCuBd8eSwFFV1", //EUR
                "new_plan_value" => "0.00",
            ],

            //PAID PLAN
            [
                "old_plan_id" => "price_1It8ZfCBmBWVCuBdJq1WThzh", //GBP
                "old_plan_value" => "12.00",
                "new_plan_id" => "price_1MErHxCBmBWVCuBdtZyTFH6D", //GBP
                "new_plan_value" => "22.00",
            ],
            [
                "old_plan_id" => "price_1It8Z0CBmBWVCuBdRPatw13P", //USD
                "old_plan_value" => "17.00",
                "new_plan_id" => "price_1MErIdCBmBWVCuBdfoub9Irj", //USD
                "new_plan_value" => "26.00",
            ],
            [
                "old_plan_id" => "price_1It8aFCBmBWVCuBdpS0WqGFe", //SEK
                "old_plan_value" => "140.00",
                "new_plan_id" => "price_1MErHOCBmBWVCuBduSGHskBf", //SEK
                "new_plan_value" => "249.00",
            ],
            [
                "old_plan_id" => "price_1IlYRUCBmBWVCuBdBsmscuFH", //EUR
                "old_plan_value" => "14.00",
                "new_plan_id" => "price_1MErJACBmBWVCuBd193MrjzP", //EUR
                "new_plan_value" => "24.00",
            ],
            [
                "old_plan_id" => "plan_HInwnnf5eqtMLB", //EUR
                "old_plan_value" => "25.00",
                "new_plan_id" => "price_1MErJACBmBWVCuBd193MrjzP", //EUR
                "new_plan_value" => "24.00",
            ],
            [
                "old_plan_id" => "plan_HInwfnuKz8QiE2", //EUR
                "old_plan_value" => "59.00",
                "new_plan_id" => "price_1MErJACBmBWVCuBd193MrjzP", //EUR
                "new_plan_value" => "24.00",
            ],
            [
                "old_plan_id" => "plan_HInwPAWQvXHRBc", //EUR
                "old_plan_value" => "5.00",
                "new_plan_id" => "price_1MErJACBmBWVCuBd193MrjzP", //EUR
                "new_plan_value" => "24.00",
            ],
        ];

        $current_plan_id = null;
        $new_plan_id = null;
        $current_plan_quantity = null;

        $currentSubscription = $company->subscriptions()->first();

        if (!$currentSubscription) {
            throw new Exception("NO Subscription found.");
        }




        if ($currentSubscription->valid()) {
            throw new Exception("Subscription is valid.");
        }

        foreach ($plan_map as $plan) {
            if ($currentSubscription->stripe_plan == $plan['old_plan_id']) {
                $current_plan_id = $plan['old_plan_id'];
                $new_plan_id = $plan['new_plan_id'];
                if ($request->has('quantity')) {
                    $current_plan_quantity = $request->quantity;
                } else {
                    $current_plan_quantity = $currentSubscription->quantity;
                }
                $current_plan_quantity = $currentSubscription->quantity;
            }
        }
        //SUBSCRIBING CUSTOMER TO NEW PLAN
        $company->createOrGetStripeCustomer();
        $plans = Plan::get();
        $plan = $plans->where('plan_id', $new_plan_id)->first();
        if (!$plan) {
            throw new Exception("Plan not found.");
        }
        if ($plan->isLicensed()) {
            $plan->client = $plan->client * $current_plan_quantity;
            $plan->users = $plan->users * $current_plan_quantity;
        }
        $payment_method = $company->defaultPaymentMethod()->id;
        try {

            $data = $company->invoices()[0]->period_end;
            $date = Carbon::createFromTimestamp($data);
            $billing_day = Carbon::parse($date)->format('d');
            $next_month = Carbon::parse($date)->addMonth()->format('m');
            $year = Carbon::parse($date)->addMonth()->format('Y');
            $billing_day = Carbon::parse($currentSubscription->created_at)->format('d');
            $next_month = Carbon::parse($currentSubscription->created_at)->addMonth()->format('m');
            $year = Carbon::parse($currentSubscription->created_at)->addMonth()->format('Y');

            // $trail_period = Carbon::now()->diffInDays(Carbon::parse($year . '-' . $next_month . '-' . $billing_day + 1));
            $company
                ->newSubscription('Subscription', $new_plan_id)
                // ->trialDays(30)
                // ->anchorBillingCycleOn(Carbon::parse($year . '-' . $next_month . '-' . $billing_day))
                ->anchorBillingCycleOn(strtotime(Carbon::parse($year . '-' . $next_month . '-' . $billing_day)))
                ->quantity($plan->isFree() ? 1 : $current_plan_quantity)
                ->create(
                    $payment_method,
                    [],
                    ['proration_behavior' => 'none']
                );
        } catch (IncompletePayment $th) {
            $invoice = $company->findInvoice($th->payment->invoice);
            if ($invoice) {
                $invoice->void();
            }
            throw new Exception("incomplete payment: " . $th->getMessage());
        }
        return true;
    }


    static function checkForPlan(Company $company)
    {
        $plans = collect($company->plans());
        $current_plan = $plans->where('is_selected', true)->first();
        if (!$current_plan) {
            throw new Exception("no plan found for this company");
        }
        if (!$current_plan['is_2022']) {
            throw new Exception("this company is connected to old plan");
        }
        $company_current_users = $company->users()->count();
        $company_current_clients = $company->clients()->count();
        if ($current_plan['users'] < $company_current_users) {
            throw new Exception("this company has more users then it's plan");
        }
        if ($current_plan['client'] < $company_current_clients) {
            throw new Exception("this company has more clients then it's plan");
        }

        return true;
    }

    static function syncPlan($plan_id)
    {
        $plan = PromocodeManager::getPlan($plan_id);
        $currency = null;
        if (isset($plan->currency) && isset($plan->interval)) {
            $is_yearly = null;
            switch ($plan->currency) {
                case 'sek':
                    $currency = 'kr';
                    break;
                case 'eur':
                    $currency = '€';
                    break;
                case 'usd':
                    $currency = '$';
                    break;
                case 'gbp':
                    $currency = '£';
                    break;

                default:
                    $currency = null;
                    break;
            }
            switch ($plan->interval) {
                case 'year':
                    $is_yearly = 1;
                    break;
                case 'month':
                    $is_yearly = 0;
                    break;

                default:
                    $is_yearly = null;
                    break;
            }
        }
        $db_plan = Plan::where('is_free', 0)->where('currency', $currency)->where('is_yearly', $is_yearly)->first();
        if ($db_plan) {
            return $db_plan;
        }
        return null;
    }
}
