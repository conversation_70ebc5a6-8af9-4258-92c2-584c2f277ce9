<?php

namespace App\Traits;

use App\Client;
use App\ClientSMS;
use App\Company;
use App\CompanyBooking;
use App\CompanyPaymentMethod;
use App\CompanyPlatform;
use App\Mail\SMSUnsendMail;
use App\Setting;
use App\ShortUrl;
use App\SMSTemplate;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

trait SMS
{
    public static function GET_COUNTRY_CODES()
    {
        return config('sms.SUPPORTED_COUNTRY_CODES');
    }

    public static function FORMAT_NUMBER($code, $number, $add_plus = true)
    {
        // remove number from start
        $code = str_replace('+', '', $code);
        // Remove leading zeros
        $number = ltrim($number, '0');
        // Remove spaces
        $number = str_replace(' ', '', $number);

        return ($add_plus ? '+' : '') . $code . $number;
    }

    public static function IS_ALPHA_NUMBERIC_SENDER_ID_SUPPORTED($code)
    {

        $code = str_replace('+', '', $code);
        $code = trim($code);

        $supported_codes_list = config('sms.ALPHA_SENDER_IDS_SUPPORTED_COUNTRY_CODES');

        if (in_array($code, $supported_codes_list)) {
            return true;
        }

        return false;
    }

    public static function FORMAT_NUMBER_CHECK_CODE($code, $number, $add_plus = true): string | null | false
    {
        if (!$code || !$number) {
            return false;
        }

        $supported_country_codes = SMS::GET_COUNTRY_CODES();
        if (!in_array($code, $supported_country_codes)) {
            return null;
        }

        return SMS::FORMAT_NUMBER($code, $number);
    }

    public static function REPLACE_KEYS(
        string $message,
        User $user = null,
        Client $client = null,
        CompanyBooking $booking = null
    ) {
        $keys = collect();
        // Company
        // Auth user
        if ($user) {
            // $keys->put("clinic_booking_page")
            $keys->put('clinic_name', $user->company->company_name);
            $keys->put('clinic_location', $user->company->address());
            $keys->put('clinic_phone', SMS::FORMAT_NUMBER($user->company->country_code, $user->company->mobile_number));

            // Auth user
            $keys->put('practitioner_name', $user->first_name . ' ' . $user->last_name);

            // Company Booking portal url
            $keys->put('booking_portal_url', ShortUrl::shorten(BookingManager::getPortalUrl($user->company)));
        }

        // Client
        if ($client) {
            $keys->put('client_first_name', $client->first_name);
            $keys->put('client_last_name', $client->last_name);
            $keys->put('client_cell_phone', SMS::FORMAT_NUMBER($client->country_code, $client->phone_number));
            $keys->put('client_email', $client->email);

            // Unsubscribe to marketing for client
            $keys->put('unsubscribe_marketing_link', ShortUrl::shorten($client->unsubscribeMarketingUrl()));
        }

        // Booking
        if ($booking) {
            $keys->put('booking_date', Setting::formateDate($booking->company, Carbon::parse($booking->start_at)));
            $keys->put('booking_start_time', Setting::formateTime($booking->company, Carbon::parse($booking->start_at)));
            $keys->put('booking_end_time', Setting::formateTime($booking->company, Carbon::parse($booking->end_at)));

            // Booking service
            $keys->put('service_name', $booking->service->name);
            $keys->put('service_duration', $booking->service->duration);
            // $keys->put('service_description', $booking->service->description);
            $keys->put('service_price', $booking->service->price);

            // Company Booking portal url with service
            $keys->put('booking_portal_service_url', ShortUrl::shorten(BookingManager::getPortalUrl($user->company, null, null, $booking->service_id)));
        }

        // IDK
        // clinic_booking_page
        // service_vat_rate
        // service_description
        // service_treatment_description
        // clinic_cancellation_code

        foreach ($keys->toArray() as $key => $value) {
            $message = str_replace('{{' . $key . '}}', $value, $message);
        }

        return $message;
    }

    public static function BULK_GENERATE_MESSAGE_KEYS(string $message)
    {
        $keys = SMS::GET_KEYS_IN_MESSAGE($message);
        foreach ($keys as $key => $value) {
            $replaceString = '${' . $value . '}';
            $message = str_replace('{{' . $key . '}}', $replaceString, $message);
        }

        return $message;
    }

    public static function GET_KEYS_IN_MESSAGE(string $message)
    {
        $pattern = '/{{(.*?)}}/';
        preg_match_all($pattern, $message, $matches);
        $keys = $matches[1];

        $newKeys = collect();
        foreach ($keys as $key) {
            $newKeys->put($key, SMS::GET_SHORT_KEY($key));
        }

        return $newKeys;
    }

    public static function CONVERT_TO_SHORT_KEYS(string $message, array $keys)
    {
        $newKeys = collect();
        foreach ($keys as $key) {
            $newKeys->push(SMS::GET_SHORT_KEY($key));
        }
        $newKeys = $newKeys->toArray();
    }

    public static function GET_SHORT_KEY(string $key)
    {
        $newKey = collect();
        $pieces = explode('_', $key);
        foreach ($pieces as $piece) {
            $newKey->push(substr($piece, 0, 2));
        }

        return $newKey->join('_');
    }

    public static function GENERATE_PARAMETERS(
        Collection $keys,
        Collection $parameters,
        string $number,
        Client $client = null,
        User $user = null,
        CompanyBooking $booking = null
    ) {
        $client_key_values = collect();

        if ($user) {
            $client_key_values->put(SMS::GET_SHORT_KEY('clinic_name'), $user->company->company_name);
            $client_key_values->put(SMS::GET_SHORT_KEY('clinic_location'), $user->company->address());
            $client_key_values->put(SMS::GET_SHORT_KEY('clinic_phone'), SMS::FORMAT_NUMBER($user->company->country_code, $user->company->mobile_number));

            // Auth user
            $client_key_values->put(SMS::GET_SHORT_KEY('practitioner_name'), $user->first_name . ' ' . $user->last_name);

            // Company Booking portal url
            $client_key_values->put(SMS::GET_SHORT_KEY('booking_portal_url'), ShortUrl::shorten(BookingManager::getPortalUrl($user->company)));
        }

        // Client
        if ($client) {
            $client_key_values->put(SMS::GET_SHORT_KEY('client_first_name'), $client->first_name);
            $client_key_values->put(SMS::GET_SHORT_KEY('client_last_name'), $client->last_name);
            $client_key_values->put(SMS::GET_SHORT_KEY('client_cell_phone'), SMS::FORMAT_NUMBER($client->country_code, $client->phone_number));
            $client_key_values->put(SMS::GET_SHORT_KEY('client_email'), $client->email);

            // Unsubscribe to marketing for client
            $client_key_values->put(SMS::GET_SHORT_KEY('unsubscribe_marketing_link'), ShortUrl::shorten($client->unsubscribeMarketingUrl()));
        }

        // Booking
        if ($booking) {
            $client_key_values->put(SMS::GET_SHORT_KEY('booking_date'), Setting::formateDate($booking->company, Carbon::parse($booking->start_at)));
            $client_key_values->put(SMS::GET_SHORT_KEY('booking_start_time'), Setting::formateTime($booking->company, Carbon::parse($booking->start_at)));
            $client_key_values->put(SMS::GET_SHORT_KEY('booking_end_time'), Setting::formateTime($booking->company, Carbon::parse($booking->end_at)));

            // Booking service
            $client_key_values->put(SMS::GET_SHORT_KEY('service_name'), $booking->service->name ?? "");
            $client_key_values->put(SMS::GET_SHORT_KEY('service_duration'), $booking->service->duration ?? "");
            $client_key_values->put(SMS::GET_SHORT_KEY('service_price'), $booking->service->price ?? "");

            // Company Booking portal url with service
            $client_key_values->put(SMS::GET_SHORT_KEY('booking_portal_service_url'), ShortUrl::shorten(BookingManager::getPortalUrl($user->company, null, null, $booking->service_id)));
        }

        foreach ($keys->values()->toArray() as $key) {
            $parameters[$key] = collect($parameters[$key])->put($number, isset($client_key_values[$key]) ? $client_key_values[$key] : "");
        }

        return $parameters;
    }

    public static function GENERATE_DEFAULT_KEYS()
    {
        $keys = collect();
        $keys->put(SMS::GET_SHORT_KEY('clinic_name'), ['default' => 'Clinic Name']);
        $keys->put(SMS::GET_SHORT_KEY('clinic_location'), ['default' => 'Clinic Location']);
        $keys->put(SMS::GET_SHORT_KEY('clinic_phone'), ['default' => 'Clinic Phone']);
        $keys->put(SMS::GET_SHORT_KEY('practitioner_name'), ['default' => 'Practitioner Name']);
        $keys->put(SMS::GET_SHORT_KEY('client_first_name'), ['default' => 'Client First Name']);
        $keys->put(SMS::GET_SHORT_KEY('client_last_name'), ['default' => 'Client Last Name']);
        $keys->put(SMS::GET_SHORT_KEY('client_cell_phone'), ['default' => 'Client Cell Phone']);
        $keys->put(SMS::GET_SHORT_KEY('client_email'), ['default' => 'Client Email']);
        $keys->put(SMS::GET_SHORT_KEY('booking_date'), ['default' => 'Booking Date']);
        $keys->put(SMS::GET_SHORT_KEY('booking_start_time'), ['default' => 'Booking Start Time']);
        $keys->put(SMS::GET_SHORT_KEY('booking_end_time'), ['default' => 'Booking End Time']);
        $keys->put(SMS::GET_SHORT_KEY('service_name'), ['default' => 'Service Name']);
        $keys->put(SMS::GET_SHORT_KEY('service_duration'), ['default' => 'Service Duration']);
        $keys->put(SMS::GET_SHORT_KEY('service_description'), ['default' => 'Service Description']);
        $keys->put(SMS::GET_SHORT_KEY('service_price'), ['default' => 'Service Price']);
        $keys->put(SMS::GET_SHORT_KEY('booking_portal_service_url'), ['default' => 'https://meridiq.com']);
        $keys->put(SMS::GET_SHORT_KEY('booking_portal_url'), ['default' => 'https://meridiq.com']);
        $keys->put(SMS::GET_SHORT_KEY('unsubscribe_marketing_link'), ['default' => 'https://meridiq.com']);

        return $keys;
    }

    /**
     * get the stripe-id of the price based on given quantity and currency
     *
     * @param string $quantity quantity of sms needed.
     * @param string $currency currency in which customer wants to pay
     * @return string stripe id of the price based on given quantity and currency
     */
    public static function GET_SMS_PRICE($quantity, $currency)
    {
        $platform = CompanyPlatform::SMS;
        $quantity_based_price = collect(config('stripe.prices'))->filter(function ($price) use ($quantity) {
            if ($price['platform'] == CompanyPlatform::SMS && $price['quantity'] == $quantity) {
                return true;
            } else {
                return false;
            }
        })->values();
        if (count($quantity_based_price) <= 0) {
            throw new Exception('price not found for this quantity');
        }
        $price = collect($quantity_based_price[0]['prices'])->filter(function ($price) use ($currency) {
            return strtolower($price['currency']) === strtolower($currency);
        })->values();
        if (count($price) <= 0) {
            throw new Exception('price not found for this currency');
        }

        return $price[0];
    }

    public static function SEND_BOOKING_SMS(
        string $message_template,
        CompanyBooking $booking,
        Client $client,
        Company $company,
        string $type = "SEND_BOOKING_SMS",
        bool $send_to_user = false,
    ) {
        $sms_credits = $company->sms_credits;
        if (!$message_template) return 1;
        try {
            $number = null;
            $user = $booking->user;
            $errors = collect();
            $language = Setting::getSetting($company, Setting::LANGUAGE)?->value;

            if ($send_to_user) {
                $number = SMS::FORMAT_NUMBER_CHECK_CODE($user->country_code, $user->mobile_number);

                if ($number === false) {
                    $errors->push(['user' => $user, 'reason' => __('sms.users_phone_number_or_country_code_not_found')]);
                }
                if ($number === null) {
                    $errors->push(['user' => $user, 'reason' => __('sms.country_not_supported')]);
                }

                if ($errors->count()) {
                    Mail::to($company->email)->locale($language ?? app()->getLocale())->send(new SMSUnsendMail($user->company, null, $errors));
                }
            } else {
                $number = SMS::FORMAT_NUMBER_CHECK_CODE($client->country_code, $client->phone_number);

                if ($number === false) {
                    $errors->push(['client' => $client, 'reason' => __('sms.clients_phone_number_or_country_code_not_found')]);
                }
                if ($number === null) {
                    $errors->push(['client' => $client, 'reason' => __('sms.country_not_supported')]);
                }

                if ($errors->count()) {
                    Mail::to($company->email)->locale($language ?? app()->getLocale())->send(new SMSUnsendMail($company, $errors));
                }
            }
            if (!$number) return 1;

            $msg = SMS::REPLACE_KEYS($message_template, $user, $client, $booking);

            $length = Sinch::getPartLength($msg, $number);

            if ($sms_credits->credits < 25) {
                $auto_pay_success = SMS::tryAutoPay($company);
                if ($auto_pay_success) {
                    $sms_credits->refresh();
                }
            }

            if ($sms_credits->credits < $length) {
                if ($send_to_user) {
                    Mail::to($company->email)->locale($language ?? app()->getLocale())->send(new SMSUnsendMail($company, null, collect([['user' => $user, 'reason' => __('sms.insufficient_sms_credits')]])));
                } else {
                    Mail::to($company->email)->locale($language ?? app()->getLocale())->send(new SMSUnsendMail($company, collect([['client' => $client, 'reason' => __('sms.insufficient_sms_credits')]])));
                }
                return 1;
            }

            $is_alpha_sender_id_supported = SMS::IS_ALPHA_NUMBERIC_SENDER_ID_SUPPORTED($client->country_code);
            $sender_id = $is_alpha_sender_id_supported ? Setting::getSetting($company, Setting::SMS_SENDER_ID)?->value ?? null : null;
            $response = Sinch::sendSMS($msg, [$number], $sender_id, $client->country_code);

            if ($response) {
                $sms_credits->credits = $sms_credits->credits - $length;
                $sms_credits->save();
                activity()->enableLogging();
                if ($send_to_user) {
                    $activity = activity('sms_send')->performedOn($user);

                    $activity = $activity->by($company);
                    $activity = $activity->log("SMS has been sent to $number from automatic booking template");
                } else {
                    $activity = activity('sms_send')->performedOn($client);

                    $activity = $activity->by($company);
                    $activity = $activity->log("SMS has been sent to $number from automatic booking template");
                }

                ClientSMS::create([
                    'text' => $response['body'],
                    'number' => $response['to'],
                    'type' => $type,
                    'log_id' => $activity?->id,
                    'company_id' => $company->id,
                    'total_message_count' => $length,
                    'client_after_care_id' => false,
                    'batch_id' => $response['id'],
                    'client_id' => $send_to_user ? null : $client->id,
                    'user_id' => $user->id,
                ]);
            }
        } catch (\Throwable $th) {
            report($th);
            return 1;
        }
    }

    public static function CHECK_KEY_IS_ON_AND_RETURN_TEMPLATE(
        Company $company,
        string $on_key,
        string $template_key,
        bool $check_enabled = false,
    ): SMSTemplate | null {
        if ($check_enabled === false) {
            $on_value = Setting::getSetting($company, $on_key);
            if (!$on_value) {
                return null;
            }
            if (!$on_value->value) {
                return null;
            }
        }

        $template_setting = Setting::getSetting($company, $template_key);
        if (!$template_setting) {
            return null;
        }
        $template_id = $template_setting->value;
        if (!$template_id) {
            return null;
        }

        $template = SMSTemplate::query()->where('company_id', $company->id)->find($template_id);

        return $template;
    }

    public static function tryAutoPay(Company $company)
    {
        return false;
        $sms_credit = $company->sms_credits()->firstOrCreate([], ['credits' => 0]);

        if (!$sms_credit->auto_pay) return false;
        if ($sms_credit->credits >= 25) return false;

        $auto_pay_sms_counts = $sms_credit->auto_pay_sms_counts;
        $customer = $company->createOrGetStripeCustomer();
        $currency = $customer->currency;
        $currency = strtolower($customer->currency);

        if (!$currency) {
            $customerCountry = $company->country;
            $countryDetail = collect(config('stripe.country'))->filter(function ($country) use ($customerCountry) {
                return in_array($customerCountry, $country['countries']);
            })->first();
            $currency = strtolower($countryDetail['currency']);
        }

        $paymentMethod = CompanyPaymentMethod::getPaymentMethodByLabel($company, CompanyPaymentMethod::SMS);
        $invoice = null;
        $price = SMS::GET_SMS_PRICE($auto_pay_sms_counts, strtoupper($currency)); //SMS traits

        try {
            $default_taxes = $company->subscriptionTaxRates();
            $invoice = CustomInvoiceManager::generateInvoice($company->stripe_id, $price['stripe_id'], $default_taxes, 1);
            if ($invoice) {
                $payment = CustomInvoiceManager::payInvoice($invoice->id, $paymentMethod->payment_method_id);

                if (!$payment) return false;

                if ($payment) {
                    $sms_credit->credits = $sms_credit->credits + $auto_pay_sms_counts;
                    $sms_credit->save();

                    return true;
                }
            }
            return false;
        } catch (\Throwable $th) {
            return false;
        }
    }
}
