<?php

namespace App\Traits;

use App\ClientAfterCare;
use App\Company;
use App\Setting;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use Throwable;

trait PortalManager
{

    static function getPortalUrl()
    {
        $base_url = config('booking.BOOKING_PAGE_URL');
        return $base_url;
    }

    static function getAfterCareUrl(Company $company, ClientAfterCare $client_after_care, $lang = null)
    {
        $base_url = config('booking.PORTAL_URL');
        $after_care_key = Crypt::encrypt($client_after_care->id);

        if ($lang) {
            $lang_key =  $lang;
        } else {
            $lang_key =  Setting::getSetting($company, Setting::CUSTOMER_LANGUAGE)->value;
        }
        $client_after_care_url = $base_url . '/questionary-loc/' . $company->encrypted_id . '?lang=' . $lang_key . '&after_care_key=' . $after_care_key;
        return $client_after_care_url;
    }
}
