<?php

namespace App\Traits;

use App\UserDevice;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Throwable;

trait TimeZoneManager
{
    static function getCarbonApprovedTimeZone($time_zone)
    {
        try {
            if (!$time_zone) {
                $time_zone = 'Europe/Stockholm';
            }
            Carbon::now()->setTimezone($time_zone);
        } catch (Throwable $th) {
            $time_zone = 'Europe/Stockholm';
        }
        return $time_zone;
    }

    static function getTimeZoneCarbonNow($time_zone)
    {
        $time_zone = TimeZoneManager::getCarbonApprovedTimeZone($time_zone);
        $now = Carbon::parse(
            Carbon::now()->setTimezone($time_zone)->format('Y-m-d H:i:s')
        );
        return $now;
    }
}
