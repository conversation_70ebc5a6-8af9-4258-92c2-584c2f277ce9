<?php

namespace App\Traits;

use App\Company;
use App\CompanyBooking;
use App\CompanyBookingClient;
use App\CompanyBusinessHour;
use App\Setting;
use App\SubscriptionCancellationData;
use App\UserDevice;
use Carbon\Carbon;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use Throwable;

trait SubscriptionManager
{
    static function getCancelSubscriptionUrl(SubscriptionCancellationData $subscription_cancellation_data)
    {
        $base_url = config('booking.PORTAL_URL');

        $key = Crypt::encrypt($subscription_cancellation_data->id);

        $cancellation_url = $base_url . '/cancel-subscription?key=' . $key . '&lang=' . app()->getLocale();
        return $cancellation_url;
    }
}
