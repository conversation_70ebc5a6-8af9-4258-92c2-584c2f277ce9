<?php

namespace App\Traits;

use App\UserDevice;
use Illuminate\Support\Str;

trait useDevice
{
    public function createDevice()
    {
        $device = $this->devices()->create([
            'token' => $this->generateDeviceToken(),
            'otp_expire_at' => $this->getOtpExpireAt(),
            'otp' => $this->generateOtp(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        return $device;
    }

    public function updateDevice(UserDevice $device)
    {
        $device->otp = $this->generateOtp();
        $device->otp_expire_at = $this->getOtpExpireAt();

        $device->save();
        return $device;
    }

    public function devices()
    {
        return $this->hasMany(UserDevice::class);
    }

    private function generateDeviceToken()
    {
        $token = Str::random(30);
        while (UserDevice::where('token', $token)->count()) {
            $token = Str::random(30);
        }
        return $token;
    }

    private function generateOtp()
    {
        return rand(1000, 9999);
    }

    private function getOtpExpireAt()
    {
        $seconds = config('device.otp_expire_time', 600);
        return now()->addSeconds($seconds);
    }
}
