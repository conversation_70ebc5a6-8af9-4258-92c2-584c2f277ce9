<?php

namespace App\Traits;

use Illuminate\Support\Facades\Crypt;
use Stripe\Stripe;
// use Stripe\StripeClient;
use Throwable;

trait ReceiptManager
{
    static function getPaymentReceipt($stripe_payment_id)
    {
        $stripe = new \Stripe\StripeClient(secret_env('STRIPE_SECRET'));
        $payment = $stripe->paymentIntents->retrieve(
            $stripe_payment_id,
            []
        );
        return $payment->charges->data[0]->receipt_url;
    }
}
