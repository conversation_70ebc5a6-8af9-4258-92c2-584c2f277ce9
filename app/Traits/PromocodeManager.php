<?php

namespace App\Traits;

use App\ClientAfterCare;
use App\Company;
use App\Setting;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use Lara<PERSON>\Cashier\Cashier;
use Stripe\Coupon;
use Stripe\Plan;
use Stripe\Stripe;
use Stripe\Subscription;
use Throwable;

trait PromocodeManager
{

    static function getPromocode($code_name)
    {
        Stripe::setApiKey(secret_env('STRIPE_SECRET'));
        try {
            return Coupon::retrieve($code_name);
        } catch (\Throwable $th) {
            return null;
        }
    }

    static function applyPromocodeToSubscription($code_name, $subscription_id)
    {
        Stripe::setApiKey(secret_env('STRIPE_SECRET'));
        try {
            $subscription = Subscription::retrieve($subscription_id);
        } catch (\Throwable $th) {
            //throw $th;
        }

        if ($subscription) {
            $subscription->coupon = $code_name;
            $subscription->save();
        }

        return $subscription;
    }

    static function removePromocodeFromSubscription($subscription_id)
    {
        Stripe::setApiKey(secret_env('STRIPE_SECRET'));
        $subscription = null;
        try {
            $subscription = Subscription::retrieve($subscription_id);
        } catch (\Throwable $th) {
            //throw $th;
        }

        if ($subscription) {

            $subscription->coupon = null;
            $subscription->save();
        }
    }

    static function getSubscription($subscription_id)
    {
        Stripe::setApiKey(secret_env('STRIPE_SECRET'));
        try {
            return Subscription::retrieve($subscription_id);
        } catch (\Throwable $th) {
            return null;
        }
    }

    static function getPlan($plan_id)
    {
        Stripe::setApiKey(secret_env('STRIPE_SECRET'));
        try {
            return Plan::retrieve($plan_id);
        } catch (\Throwable $th) {
            return null;
        }
    }
    static function getPlanBySubscription($subscription_id)
    {
        $subscription = PromocodeManager::getSubscription($subscription_id);
        if (!$subscription) {
            return null;
        }

        if (isset($subscription->items) && isset($subscription->items->data)) {
            if (is_array($subscription->items->data) && count($subscription->items->data) > 0) {
                if (isset($subscription->items->data[0]->plan)) {
                    return $subscription->items->data[0]->plan;
                }
            }
        }
        return null;
    }
}
