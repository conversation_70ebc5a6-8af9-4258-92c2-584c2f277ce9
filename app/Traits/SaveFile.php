<?php

namespace App\Traits;

use App\Exceptions\StorageLimitExceed;
use App\File;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use ZipArchive;

trait SaveFile
{
    /**
     * @param  string  $filename Filename with extension
     */
    protected function saveFile($file, string $model_name, $user = null, $compress = false, $generate_thumb = false, $filename = null)
    {
        if (! $user) {
            $user = Auth::user();
        }
        $extension = $this->getExtension($file);

        $created_at = $this->getDateTime($file, $user?->company?->timezone ?? 'Europe/Stockholm');

        if (in_array(Str::lower($extension), ['jpg', 'png', 'jpeg', 'gif'])) {
            if (App::environment(['testing', 'local', 'staging'])) {
                $model_name = 'testing/' . Str::camel($user->company->company_name) . '_' . $user->company->id . '/' . $model_name . '/';
            } else {
                $model_name = Str::camel($user->company->company_name) . '_' . $user->company->id . '/' . $model_name . '/';
            }
            $resize_file = Image::make($file)->orientate();

            // $destinationPath = storage_path('app/public/' . $model_name . '/');

            $upload_file_name = $this->generateFileName($file, $model_name) . '.' . $extension;

            // if (!File::isDirectory($destinationPath)) {
            //     File::makeDirectory($destinationPath, 0777, true, true);
            // }
            //$resize_file->resize(100,null);

            if ($compress) {
                $resize_file->resize(500, 500, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }

            // if (
            //     ($user->company->subscribed('Subscription')) && (($user->company->activeSubscription()->plan->storage * 1000000000)
            //         < ($user->company->files()->sum('size') + $image_size))
            // ) {
            //     throw new StorageLimitExceed();
            // }

            if ($generate_thumb) {
                $thumbnail_file = Image::make($file)->orientate();
                $thumbnail_file->resize(200, 200, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
                $thumbnail_path = Storage::disk('s3')->put($model_name . 'thumbnail/' . $upload_file_name, $thumbnail_file->stream()->__toString());
                $thumbnail_path = $model_name . 'thumbnail/' . $upload_file_name;
            }

            $path = Storage::disk('s3')->put($model_name . $upload_file_name, $resize_file->stream()->__toString());

            $path = $model_name . $upload_file_name;
        } else {
            // if (
            //     ($user->company->subscribed('Subscription')) && (($user->company->activeSubscription()->plan->storage * 1000000000)
            //         < ($user->company->files()->sum('size') + $file->getSize()))
            // ) {
            //     throw new StorageLimitExceed();
            // }
            if (App::environment(['testing', 'local', 'staging'])) {
                $model_name = 'testing/' . Str::camel($user->company->company_name) . '_' . $user->company->id . '/' . $model_name;
            } else {
                $model_name = Str::camel($user->company->company_name) . '_' . $user->company->id . '/' . $model_name;
            }
            $upload_file_name = $filename ?? $this->generateFileName($file, $model_name) . '.' . $extension;
            Storage::put("{$model_name}/{$upload_file_name}", file_get_contents($file->getRealPath()));
            $path = "{$model_name}/{$upload_file_name}";
        }

        Cache::forget("storage-usage-{$user->company->id}");

        return File::create([
            'filename' => $path,
            'original_filename' => $file?->getClientOriginalName() ?? null,
            'url' => Storage::disk('s3')->url($path),
            'user_id' => $user->id,
            'size' => Storage::disk('s3')->size($path),
            'company_id' => $user->company_id,
            'thumbnail' => $thumbnail_path ?? null,
            'created_at' => $created_at,
        ]);
    }

    public function generateStoreQuestionary($data, $view, $model_name, $user = null)
    {
        if (! $user) {
            $user = Auth::user();
        }

        if (App::environment(['testing', 'local', 'staging'])) {
            $model_name = 'testing/' . Str::camel($user->company->company_name) . '_' . $user->company->id . '/' . $model_name;
        } else {
            $model_name = Str::camel($user->company->company_name) . '_' . $user->company->id . '/' . $model_name;
        }

        $path = PDF::loadView($view, $data);

        $filename = Str::random(20);

        // Make sure the filename does not exist, if it does, just regenerate
        while (Storage::disk('s3')->exists($model_name . $filename . '.pdf')) {
            $filename = Str::random(20);
        }

        $upload_file_name = $filename . '.pdf';

        Storage::put("{$model_name}/{$upload_file_name}", $path->output());

        $path = "{$model_name}/{$upload_file_name}";

        Cache::forget("storage-usage-{$user->company->id}");

        return File::create([
            'filename' => $path,
            'url' => Storage::disk('s3')->url($path),
            'user_id' => $user->id,
            'size' => Storage::disk('s3')->size($path),
            'company_id' => $user->company_id,
        ]);
    }

    function generateFilePath(string $model_name, $user = null, $filename = null, $extension = 'jpg')
    {
        if (! $user) {
            $user = Auth::user();
        }

        if (!App::isProduction()) {
            $model_name = 'testing/' . Str::camel($user->company->company_name) . '_' . $user->company->id . '/' . $model_name . '/';
        } else {
            $model_name = Str::camel($user->company->company_name) . '_' . $user->company->id . '/' . $model_name . '/';
        }

        if (!$filename) {
            $filename = Str::random(20);

            // Make sure the filename does not exist, if it does, just regenerate
            while (Storage::disk('s3')->exists($model_name . $filename . '.' . $extension)) {
                $filename = Str::random(20);
            }
        }

        return $model_name . $filename . '.' . $extension;
    }

    /**
     * @return string
     */
    protected function generateFileName($file, $path)
    {
        $filename = Str::random(20);

        // Make sure the filename does not exist, if it does, just regenerate
        while (Storage::disk('s3')->exists($path . $filename . '.' . $file->getExtension())) {
            $filename = Str::random(20);
        }

        return $filename;
    }

    public function deleteDirectory($path)
    {
        // if (count(Storage::disk('s3')->exists($path))) {
        try {
            //code...
            Storage::disk('s3')->deleteDirectory($path);
        } catch (\Throwable $th) {
            //throw $th;
        }
        // }
    }

    public function deleteFile($path)
    {
        try {
            //code...
            if (Storage::disk('s3')->exists($path)) {
                Storage::disk('s3')->delete($path);
            }
        } catch (\Throwable $th) {
            //throw $th;
        }
    }

    public function createZip(array $metadata, $should_download = true, $filename = 'data.zip')
    {
        if (isset($metadata['filename'])) {
            $filename = $metadata['filename'];
        }
        $files = [];
        if (isset($metadata['files']) && is_array($metadata['files'])) {
            $files = $metadata['files'];
        }
        $zip = new ZipArchive();
        $zip->open(storage_path($filename), ZipArchive::CREATE);
        foreach ($files as $file) {
            $url = null;
            if (isset($file['url'])) {
                $url = $file['url'];
            }
            $file_content = null;
            if (isset($file['file_content'])) {
                $file_content = $file['file_content'];
            }

            if (! $file_content && ! $url) {
                continue;
            }
            if ($file_content) {
                if (! isset($file['filename'])) {
                    continue;
                }
            }

            if ($url) {
                $temp_filename = basename($url);
            }
            if (isset($file['filename'])) {
                $temp_filename = $file['filename'];
            }
            try {
                $zip->addFromString(
                    $temp_filename,
                    $file_content ?? file_get_contents($url)
                );
            } catch (\Throwable $th) {
                //throw $th;
            }
        }
        $zip->close();
        $data = file_get_contents(storage_path($filename));
        unlink(storage_path($filename));

        if (! $should_download) {
            return $data;
        }

        $dir = sys_get_temp_dir();
        $tmp = tempnam($dir, $filename);
        file_put_contents($tmp, $data);

        return response()->download($tmp, $filename)->deleteFileAfterSend();
    }

    protected function getExtension($file)
    {
        $extension = '';
        try {
            $extension = $file->getClientOriginalExtension();

            return $extension;
        } catch (\Throwable $th) {
        }
        try {
            $extension = $file->guessExtension();

            return $extension;
        } catch (\Throwable $th) {
        }

        return $extension;
    }

    protected function getDateTime($file, $timezone = 'Europe/Stockholm')
    {
        try {
            // Get the file path
            $filePath = $file->getRealPath();

            $exifData = @exif_read_data($filePath);

            if ($exifData && isset($exifData['DateTime'])) {
                $datetime = $exifData['DateTime']; // Example: "2025:01:17 12:34:56"
                return Carbon::createFromFormat('Y:m:d H:i:s', $datetime, $timezone)->utc();
            }

            return null;
        } catch (\Throwable $th) {
            //throw $th;
            report($th);

            return now();
        }
    }
}
