<?php

namespace App\Traits\POS;

use App\Http\Integrations\POSTaxControlUnit\Facades\POSTaxControlUnit;
use App\ReceiptType;
use Carbon\Carbon;

trait HasCCUControlCode
{
    public function generatePaidControlCode()
    {
        if ($this->control_code) {
            return $this;
        }

        $control_code = $this->generateReceiptData(ReceiptType::normal);
        $this->ctu_id = $control_code['CTUID'];
        $this->control_code = $control_code['Code'];
        $this->save();

        return $this;
    }

    public function generateReceiptCopyControlCode()
    {
        $control_code = $this->generateReceiptData(ReceiptType::kopia);
        return $control_code;
    }

    public function generateRefundControlCode()
    {
        if ($this->control_code) {
            return $this;
        }

        $control_code = $this->generateReceiptData(ReceiptType::normal, true);
        $this->ctu_id = $control_code['CTUID'];
        $this->control_code = $control_code['Code'];
        $this->save();

        return $this;
    }

    public function generateRefundCopyControlCode()
    {
        $control_code = $this->generateReceiptData(ReceiptType::kopia, true);
        return $control_code;
    }


    public function generateReceiptData(ReceiptType $type, $refund = false)
    {
        $type = $type == ReceiptType::normal ? 'normal' : 'kopia';

        $company = $this->company;

        $tax_info = $this->generateTaxLevel();

        $vat_25_percentage = $tax_info->where('percentage', 25)?->first()?->get('tax') ?? 0;
        $vat_12_percentage = $tax_info->where('percentage', 12)?->first()?->get('tax') ?? 0;
        $vat_6_percentage = $tax_info->where('percentage', 6)?->first()?->get('tax') ?? 0;
        $vat_0_percentage = $tax_info->where('percentage', 0)?->first()?->get('tax') ?? 0;

        $control_code = POSTaxControlUnit::ccu($company->ccu_register_id)
            ->getCCUCode(
                date_time: Carbon::parse($this?->paid_at ?? $this?->refunded_at ?? now())->setTimezone("Europe/Stockholm")->format('YmdHi'),
                organization_number: str_replace("-", "", $company->organization_number),
                sequence_number: $this->viva_receipt_id,
                receipt_type: $type,
                sale_amount: $refund ? $this->total_amount : $this->total_formatted,
                vat_25_percentage: $vat_25_percentage,
                vat_12_percentage: $vat_12_percentage,
                vat_6_percentage: $vat_6_percentage,
                vat_0_percentage: $vat_0_percentage,
                refund: $refund,
            );
        return $control_code;
    }
}
