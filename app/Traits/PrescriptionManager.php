<?php

namespace App\Traits;

use App\Company;
use App\CompanyPlatform;
use App\Setting;
use App\UpcomingPlatformFees;
use Carbon\Carbon;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Auth;
use Throwable;

trait PrescriptionManager
{
    static function getInviteUrl($connection_id)
    {
        //TODO::Check Url
        $base_url = config('booking.PORTAL_URL');
        $connection_key =  Crypt::encrypt($connection_id);
        $company = Auth::user()->company;
        $lang_key =  Setting::getSetting($company, Setting::LANGUAGE)->value;
        $invitation_url = $base_url.'/doctor-invitation/' . $connection_key.'?lang='. $lang_key;
        return $invitation_url;
    }

    static function getServiceCharge($amount)
    {
        //Todo::Check For updates
        $charge = $amount * config('prescription.service_charge');
        return number_format($charge, 2, '.', '');
    }

    static function getServiceChargeByCompany(Company $company, $prescription_count, $amount)
    {
        //Todo::Check For updates
        $upcoming_fees = UpcomingPlatformFees::where(['company_id'=>$company->id,'start_date'=>Carbon::now()->startOfMonth()])->first();
        if ($upcoming_fees) {
            return $prescription_count * $upcoming_fees->price;
        }
        $company_platform = CompanyPlatform::where(['company_id'=>$company->id,'platform'=>CompanyPlatform::PRESCRIPTION])->first();
        if($company_platform && $company_platform->price){
            return $prescription_count * $company_platform->price;
        }
        return $prescription_count * config('prescription.service_charge');
    }

    static function getMonthPriceByCompany(Company $company, Carbon $start_of_month)
    {
        //Todo::Check For updates
        $upcoming_fees = UpcomingPlatformFees::where(['company_id'=>$company->id,'start_date'=>Carbon::parse($start_of_month)->startOfMonth()])->first();
        if ($upcoming_fees) {
            return [
                'price'=>$upcoming_fees->price,
                'price_id'=>$upcoming_fees->price_id,
            ];
        }
        $company_platform = CompanyPlatform::where(['company_id'=>$company->id,'platform'=>CompanyPlatform::PRESCRIPTION])->first();
        if($company_platform && $company_platform->price){
            return [
                'price'=>$company_platform->price,
                'price_id'=>$company_platform->price_id,
            ];
        }
        return [
            'price'=>config('prescription.service_charge'),
            'price_id'=>config('prescription.price_id'),
        ];
    }
}