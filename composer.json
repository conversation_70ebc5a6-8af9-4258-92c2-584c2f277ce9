{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "ajcastro/eager-load-pivot-relations": "^0.3.0", "alexcrawford/lexorank-sortable": "^7.2", "barryvdh/laravel-dompdf": "*", "barryvdh/laravel-snappy": "^1.0", "cknow/laravel-money": "^7.2", "code-lts/laravel-fcm": "^1.8", "codex-team/editor.js": "^2.0", "dannyvankooten/laravel-vat": "^2.0", "doctrine/dbal": "^2.10", "fideloper/proxy": "^4.0", "fruitcake/laravel-cors": "^2.0", "google/apiclient": "^2.14", "guzzlehttp/guzzle": "^7.0.1", "hammerstone/sidecar": "^0.4.2", "intervention/image": "^2.5", "itsgoingd/clockwork": "^5.2", "laravel/cashier": "^12", "laravel/framework": "^8.0", "laravel/octane": "^1.5", "laravel/passport": "^10.0", "laravel/sanctum": "^2.15", "laravel/scout": "*", "laravel/socialite": "^5.15", "laravel/tinker": "^2.0", "laravel/ui": "^3.0", "league/flysystem-aws-s3-v3": "^1.0", "maatwebsite/excel": "^3.1", "maennchen/zipstream-php": "^2.1", "mailchimp/marketing": "^3.0", "mcamara/laravel-localization": "^1.4", "om/icalparser": "^3.1", "rap2hpoutre/fast-excel": "^3.0", "saloonphp/saloon": "^2.0", "sentry/sentry-laravel": "^4.7", "spatie/laravel-activitylog": "^3.13", "teamtnt/laravel-scout-tntsearch-driver": "^15.0", "tymon/jwt-auth": "^1.0@dev", "verumconsilium/laravel-browsershot": "^1.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "facade/ignition": "^2.3.6", "fzaninotto/faker": "^1.9.1", "laravel/sail": "^1.14", "laravel/telescope": "^4.16", "mockery/mockery": "^1.0", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"files": ["app/Http/helpers.php", "app/Http/SecretHelper.php"], "psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}