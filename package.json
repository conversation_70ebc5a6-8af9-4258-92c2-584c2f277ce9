{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "export NODE_OPTIONS=--openssl-legacy-provider && npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --openssl-legacy-provider --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "email-dev": "npm --prefix=./maizzle run dev", "email-build": "npm --prefix=./maizzle run build"}, "devDependencies": {"axios": "^0.19", "bootstrap": "^4.0.0", "cross-env": "^7.0", "jquery": "^3.2", "laravel-mix": "^5.0.1", "lodash": "^4.17.13", "popper.js": "^1.12", "resolve-url-loader": "^3.1.0", "sass": "^1.15.2", "sass-loader": "^8.0.0", "tailwindcss": "^3.3.5", "vue-template-compiler": "^2.7.16"}, "dependencies": {"puppeteer": "^10.0.0"}}