<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Subscription API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

$is_in_testing = env('APP_ENV') == 'testing';
if ($is_in_testing) {
    Route::match(['GET', 'POST'], 'complete_free_trial', 'v3\TestController@complete_free_trial');
}

Route::group(['middleware' => ['auth:sanctum', 'auth_blocked', 'verified']], function () {


    Route::group(['prefix' => 'subscription'], function () {
        Route::get('plans', 'v3\SubscriptionController@plans');

        Route::get('create', 'v3\SubscriptionController@create');
        Route::post('store', 'v3\SubscriptionController@store');

        Route::group(['middleware' => 'subscription_required'], function () {
            Route::get('expiry', 'v3\SubscriptionController@expiry');
            // Route::post('request-to-cancel', 'v3\SubscriptionController@requestToCancel');
            // Route::post('delete-subscription', 'v3\SubscriptionController@delete');
        });
    });

    Route::group(['prefix' => 'promocode'], function () {
        Route::get('show', 'v3\PromocodeController@show');
        Route::get('active', 'v3\PromocodeController@active');
    });

    Route::group(['prefix' => 'invoice'], function () {
        Route::get('', 'v3\InvoiceController@index');
        Route::get('{id}/pay', 'v3\InvoiceController@pay');
        Route::get('{id}/download', 'v3\InvoiceController@download');
        Route::get('pending', 'v3\InvoiceController@pendingSubscriptionInvoice');
        Route::delete('{id}/void', 'v3\InvoiceController@void');
    });

    Route::group(['prefix' => 'billing'], function () {
        Route::get('show', 'v3\BillingController@show');
        Route::post('update', 'v3\BillingController@update');
    });
});
