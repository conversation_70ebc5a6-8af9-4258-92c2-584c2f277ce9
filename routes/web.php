<?php

use App\CompanyReceipt;
use App\Http\Controllers\Api\v3\CompanyReceiptController;
use App\Http\Integrations\DailyCo\Controller\WebhookController as ControllerWebhookController;
use App\Http\Integrations\VivaWallet\Http\Controller\WebhookController;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


// Route::get('/pdf', function () {
// return (new CompanyReceiptController())->export($receipt);
// });

if (env('MASTER_ACCESS_USER') == true && env('MASTER_ACCESS_USER_ID')) {
    Route::get('master-access-user', 'v3\TestController@masterAccessUser');
}

Route::get('/iaf', function () {
    return view('invite_a_friend');
});

Route::get('/test', 'TestController@test');
Route::post('stripe/webhook', 'v1\WebhookController@handleWebhook');

Route::get('consent/verify/{client}', 'v1\ClientConsentController@verifyMail')->name('client.consent.verify');

Route::match(['get', 'post'], 'viva/webhooks', [WebhookController::class, 'handle'])->name('viva.webhooks');

Route::match(['get', 'post'], 'dailyco/webhooks', [ControllerWebhookController::class, 'handle'])->name('dailyco.webhooks');
