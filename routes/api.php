<?php

use App\Company;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::get('/testing', function() {
//     $company = Company::find(2);
//     $invoice = $company->invoices()->first();
//     // return $invoice->asStripeInvoice();
//     return view('invoice',[
//         'invoice' => $invoice,
//     ]);
//     // return $company->viewInvoice($invoice->id, [
//     //     'vendor' => 'Meridiq',
//     //     'vendorVat' => '24193256235',
//     //     'product' => 'Subscription',
//     //     'vat' => "78595487239",
//     // ]);
// });
// Route::get('/testing1', function() {
//     $company = Company::find(2);
//     $invoice = $company->invoices()->first();

//     return $company->viewInvoice($invoice->id, [
//         'vendor' => 'Meridiq',
//         'vendorVat' => '24193256235',
//         'product' => 'Subscription',
//         'vat' => "78595487239",
//     ]);
// });
Route::get('/marketing/unsubscribe/{id}', "v3\ClientController@marketingUnsubscribe")->middleware(['signed:relative'])->name('marketing.unsubscribe');

Route::get('/email/verify/{id}/{hash}', "v2\UserController@verified")->middleware(['signed:relative'])->name('verification.verify');

Route::middleware('throttle:6,1')->group(function () {
    Route::post('register', 'v1\CompanyController@store');
});

Route::get('generate-payment-source', 'v3\TestController@generatePaymentSource');
Route::get('generate-order-code', 'v3\TestController@generateOrderCode');

Route::get('test', 'TestController@test');
Route::get('sync-subscription', 'TestController@syncSubscription');
Route::get('change-plan-to-new', 'TestController@changePlanToNew');
Route::get('check-for-plans', 'TestController@checkForPlans');
Route::get('update-company-stripe-id', 'TestController@updateCompanyStripeId');
Route::get('get-companies-without-card', 'TestController@getCompaniesWithoutCard');

Route::get('change-plan-to-new', 'TestController@changePlanToNew');

// sdvsvdv
Route::post('login', 'v1\UserController@login');

// Route::middleware('throttle:5,1')->group(function () {
Route::post('forgot', 'v1\UserController@forgotPassword');
// });

Route::get('email/verify/{id}', 'v1\VerificationApiController@verify')->name('verificationapi.verify');
Route::get('email/resend', 'v1\VerificationApiController@resend')->name('verificationapi.resend');

Route::group(['prefix' => 'company'], function () {
    Route::get('/{id}/user', 'v1\UserController@publicIndex');

    Route::group(['prefix' => 'client_fields'], function () {
        Route::get('{id}/public', 'v1\CompanyClientExtraFieldController@indexPublic');
    });
});

Route::group(['prefix' => 'client'], function () {
    Route::middleware(['throttle:client'])->post('store/public', 'v1\ClientController@storePublic');
    Route::post('create/public', 'v1\ClientController@createPublic');
});
Route::group(['prefix' => 'setting'], function () {
    Route::get('{id}/public', 'v1\SettingController@indexPublic');
});

Route::group(['prefix' => 'letter_of_consent'], function () {
    Route::get('{id}/public', 'v1\LetterOfConsentController@indexPublic');
});

Route::group(['prefix' => 'questionary/public'], function () {
    Route::get('/', 'v1\QuestionaryController@indexPublic');
});

Route::group(['prefix' => 'url'], function () {
    Route::get('shorted/{key}', 'v2\ShortUrlController@redirect');
});

Route::group(['middleware' => ['auth:sanctum', 'auth_blocked', 'verified']], function () {
    Route::get('update-plan', 'TestController@updatePlane');
    Route::get('test', 'TestController@updatePlane');
    Route::get('logout', 'v2\UserController@logout')->withoutMiddleware(['verified']);

    Route::post('invite', 'v1\InviteAFriendController@invite');
    Route::post('get-signed-url', 'v1\FileController@getSignedUrl');

    Route::group(['prefix' => 'dashboard'], function () {
        Route::get('boxData', 'v1\DashboardController@boxData');
        Route::get('graphData', 'v1\DashboardController@graphData');
    });

    Route::group(['prefix' => 'company'], function () {
        Route::get('/usage', 'v1\CompanyController@getUsage');
        Route::get('/clients-count', 'v1\CompanyController@clientsCount');

        Route::post('/lead/store', 'v1\CompanyLeadController@store');

        Route::get('/', 'v1\CompanyController@index');
        Route::get('/excel', 'v1\CompanyController@export');

        Route::group(['prefix' => 'client_fields'], function () {
            Route::get('', 'v1\CompanyClientExtraFieldController@index');
            Route::post('store', 'v1\CompanyClientExtraFieldController@store');
            Route::post('{companyClientExtraField}/update', 'v1\CompanyClientExtraFieldController@update');
            Route::delete('{companyClientExtraField}/delete', 'v1\CompanyClientExtraFieldController@destroy');
        });

        Route::get('{company}', 'v1\CompanyController@show');
        Route::get('{company}/clients', 'v1\CompanyClientController@index');
        Route::get('{company}/users', 'v1\CompanyUserController@index');
        Route::post('{company}/update', 'v1\CompanyController@masterUpdate');
        Route::delete('/{company}/delete', 'v1\CompanyController@destroy');

        // Route::group(['middleware' => 'subscription_required'], function () {
        Route::post('/update', 'v1\CompanyController@update');
        // });
    });

    Route::group(['prefix' => 'user'], function () {
        Route::get('/', 'v1\UserController@show')->withoutMiddleware(['verified']);
        Route::get('/all', 'v1\UserController@index');
        Route::get('/{user}', 'v1\UserController@single');

        // Route::group(['middleware' => 'subscription_required'], function () {
        Route::post('/store', 'v1\UserController@store');
        Route::post('/{user}/update', 'v1\UserController@update');
        Route::get('/{user}/change-status', 'v1\UserController@changeStatus');
        Route::delete('/{user}/delete', 'v1\UserController@delete');
        Route::patch('/{id}/restore', 'v1\UserController@restore');
        // });
    });

    Route::group(['prefix' => 'treatment'], function () {
        Route::get('/', 'v1\TreatmentController@index');
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('store', 'v1\TreatmentController@store');
            Route::post('{treatment}/update', 'v1\TreatmentController@update');
            Route::delete('{treatment}/delete', 'v1\TreatmentController@delete');
            Route::get('/restore', 'v1\TreatmentController@restore');
        });
    });

    Route::group(['prefix' => 'letter_of_consent'], function () {
        Route::get('/', 'v1\LetterOfConsentController@index');
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('store', 'v1\LetterOfConsentController@store');
            Route::post('{letter_of_consent}/update', 'v1\LetterOfConsentController@update');
            Route::delete('{letter_of_consent}/delete', 'v1\LetterOfConsentController@delete');
            Route::get('/restore', 'v1\LetterOfConsentController@restore');
        });
    });

    Route::group(['prefix' => 'client'], function () {
        Route::get('/', 'v1\ClientController@index');
        Route::get('/{client}', 'v1\ClientController@index');

        Route::get('/{client}/download', 'v1\ClientController@download2');

        Route::get('/{client}/logs', 'v1\ClientController@logs');
        Route::get('/{client}/logs/aftercare', 'v1\ClientController@clientAfterCareLogs');
        Route::get('/{client}/logs/download', 'v1\ClientController@logsDownload');

        Route::get('/{client}/letter_of_consents', 'v1\ClientLetterOfConsentController@index');

        Route::get('/{client}/treatments', 'v1\ClientTreatmentController@index');
        Route::get('/{client}/treatments/{client_treatment}', 'v2\ClientTreatmentController@show');

        Route::get('/{client}/general_notes', 'v1\GeneralNoteController@index');

        Route::get('/{client}/questionary', 'v1\ClientQuestionaryController@index');
        Route::get('/{client}/questionary_data', 'v1\ClientQuestionaryDataController@index');

        Route::group(['prefix' => '{client}/consent'], function () {
            Route::group(['middleware' => 'subscription_required'], function () {
                Route::post('store', 'v1\ClientConsentController@store');
                Route::post('sendMail', 'v1\ClientConsentController@sendMail');
                Route::delete('cancel', 'v1\ClientConsentController@destroy');
            });
        });

        Route::group(['prefix' => '{client}/media'], function () {
            Route::get('', 'v1\ClientMediaController@index');
            Route::group(['middleware' => 'subscription_required'], function () {
                Route::post('store', 'v1\ClientMediaController@store');
                Route::delete('{file}/delete', 'v1\ClientMediaController@destroy');
            });
        });

        Route::group(['prefix' => '{client}/kind'], function () {
            Route::get('', 'v1\ClientKindController@index');
            // Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('store', 'v1\ClientKindController@store');
            // });
        });

        // Route::group(['middleware' => 'subscription_required'], function () {
        Route::middleware(['throttle:client'])->post('/{client}/after_care_treatment/store', 'v1\AfterCareTreatmentController@store');

        Route::middleware(['throttle:client'])->post('store', 'v1\ClientController@store');
        Route::post('{client}/update', 'v1\ClientController@update');
        Route::delete('{client}/delete', 'v1\ClientController@destroy');
        Route::delete('{client}/final-delete', 'v1\ClientController@delete');
        Route::get('{client}/restore', 'v1\ClientController@restore');
        // });
    });

    Route::group(['prefix' => 'health_questionnaire'], function () {
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('/{client}/store', 'v1\HealthQuestionnaireController@store');
            Route::post('/{client}/store/new', 'v1\HealthQuestionnaireController@storeNew');
        });
    });

    Route::group(['prefix' => 'aesthetic_interest'], function () {
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('/{client}/store', 'v1\AestheticInterestController@store');
            Route::post('/{client}/store/new', 'v1\AestheticInterestController@storeNew');
        });
    });

    Route::group(['prefix' => 'covid19'], function () {
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('/{client}/store', 'v1\Covid19Controller@store');
            Route::post('/{client}/store/new', 'v1\Covid19Controller@storeNew');
        });
    });

    Route::group(['prefix' => 'client_access'], function () {
        Route::get('/', 'v1\ClientAccessController@indexWeb');
        Route::get('/{user}/access', 'v1\ClientAccessController@userAccess');
        Route::get('/{user}', 'v1\ClientAccessController@index');
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('/{user}/store', 'v1\ClientAccessController@store');
            Route::post('/{client}/update-access', 'v1\ClientAccessController@updateAccess');
        });
    });

    Route::group(['prefix' => 'client_treatment'], function () {
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('/{client_treatment}/update', 'v1\ClientTreatmentController@update');
            Route::post('/{client}/store', 'v1\ClientTreatmentController@store');

            // Route::post('{client_treatment}/update/mobile', 'v1\ClientTreatmentController@updateMobile');
            // Route::post('{client_treatment}/delete/mobile', 'v1\ClientTreatmentController@deleteImageMobile');

            Route::delete('/{client_treatment}/delete', 'v1\ClientTreatmentController@delete');
        });
    });

    Route::group(['prefix' => 'client_letter_of_consent'], function () {
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('/{client}/store', 'v1\ClientLetterOfConsentController@store');
            Route::post('/{client_letter_of_consent}/update', 'v1\ClientLetterOfConsentController@update');
            Route::delete('/{client_letter_of_consent}/delete', 'v1\ClientLetterOfConsentController@delete');
        });
    });

    Route::group(['prefix' => 'general_note'], function () {
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('/{client}/store', 'v1\GeneralNoteController@store');
            Route::post('/{generalNote}/update', 'v1\GeneralNoteController@update');
            Route::delete('/{generalNote}/delete', 'v1\GeneralNoteController@destroy');
            Route::delete('/{generalNote}/files/{file}/delete', 'v1\GeneralNoteController@destroyFile');
        });
    });

    Route::group(['prefix' => 'template'], function () {
        Route::get('/', 'v1\TemplateController@index');
        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('/store', 'v1\TemplateController@store');
            Route::post('/{template}/update', 'v1\TemplateController@update');
            Route::delete('/{template}/delete', 'v1\TemplateController@delete');
        });
    });

    Route::group(['prefix' => 'support'], function () {
        Route::post('/store', 'v1\SupportController@store');
    });

    Route::group(['prefix' => 'setting'], function () {
        Route::get('/', 'v1\SettingController@index');

        // Route::group(['middleware' => 'subscription_required'], function () {
        Route::post('/store', 'v1\SettingController@store');
        Route::post('{company}/update', 'v1\SettingController@update');
        // });
    });

    Route::group(['prefix' => 'questionary'], function () {
        Route::get('/', 'v1\QuestionaryController@index');
        Route::get('/download', 'v1\QuestionaryController@download');
        Route::get('/{questionary}', 'v1\QuestionaryController@show');

        Route::group(['middleware' => 'subscription_required'], function () {
            Route::post('/store', 'v1\QuestionaryController@store');
            Route::post('/{questionary}/update', 'v1\QuestionaryController@update');
            Route::delete('/{questionary}/delete', 'v1\QuestionaryController@destroy');
        });

        Route::group(['prefix' => '{questionary}/question'], function () {
            Route::get('/', 'v1\QuestionaryQuestionController@index');
            Route::get('/{questionaryQuestion}', 'v1\QuestionaryQuestionController@show');

            Route::group(['middleware' => 'subscription_required'], function () {
                Route::post('/store', 'v1\QuestionaryQuestionController@store');
                Route::post('/{questionaryQuestion}/update', 'v1\QuestionaryQuestionController@update');
                Route::delete('/{questionaryQuestion}/delete', 'v1\QuestionaryQuestionController@destroy');
            });
        });

        Route::group(['prefix' => '{questionary}/answer'], function () {
            Route::group(['middleware' => 'subscription_required'], function () {
                Route::post('/store', 'v1\QuestionaryDataController@store');
            });
        });
    });

    Route::group(['prefix' => 'subscription'], function () {
        Route::get('/create', 'v1\SubscriptionController@create');
        Route::get('/edit', 'v1\SubscriptionController@edit');
        Route::post('/store', 'v1\SubscriptionController@store');
        Route::post('/update', 'v1\SubscriptionController@update');

        Route::get('invoices', 'v1\SubscriptionController@invoices');
        Route::get('invoices/{id}/pay', 'v1\SubscriptionController@invoicePay');
        Route::get('invoices/{id}/download', 'v1\SubscriptionController@invoiceDownload');
        Route::get('billing_details', 'v1\SubscriptionController@billing_details');

        Route::group(['middleware' => 'subscription_required'], function () {
            Route::delete('/delete', 'v1\SubscriptionController@destroy');
        });
    });
});
