<?php

namespace Database\Seeders;

use App\Company;
use App\CompanyService;
use App\Traits\POS\HasUniqueCode;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class GenerateProductCodeForService extends Seeder
{
    use HasUniqueCode;


    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companies = Company::select('id')->lazy();

        foreach ($companies as $company) {
            $services = CompanyService::where('product_code', null)
                ->where('company_id', $company->id)->lazy();

            DB::transaction(function () use ($services, $company) {
                $value = $this->getIncrementalValueModel($company->id);

                foreach ($services as $key => $service) {
                    $service->product_code = $value->item_sequence_number;
                    $service->save();

                    $value->item_sequence_number += 1;
                    $value->save();
                }
            });
        }
    }
}
