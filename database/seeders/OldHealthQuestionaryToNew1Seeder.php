<?php

namespace Database\Seeders;

use App\File;
use App\Client;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use VerumConsilium\Browsershot\Facades\PDF;

class OldHealthQuestionaryToNew1Seeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $clients = Client::has('health_questionaries')
            ->whereHas('health_questionaries', function ($query) {
                $query->where('data_new', '!=', null);
            })
            ->with(['health_questionaries', 'company', 'company.users'])->get();

        foreach ($clients as $index => $client) {
            $model_name = 'client_health_questionaries';

            if (App::environment('testing')) {
                $model_name = "testing/" . Str::camel($client->company->company_name) . "_" . $client->company->id . '/' . $model_name . '/';
            } else {
                $model_name = Str::camel($client->company->company_name) . "_" . $client->company->id . '/' . $model_name . '/';
            }

            $upload_file_name = $this->generateFileName($model_name) . '.pdf';

            $health_questionary = $client->health_questionaries->first();

            if ($health_questionary &&  $health_questionary->data_new) {
                PDF::loadView('exports.client_old_health_questionary_1', ['client' => $client])
                    ->waitUntilNetworkIdle()
                    ->format('A4')
                    ->margins(15, 0, 15, 0)
                    // ->setNodeBinary("C:\\PROGRA~1\\nodejs\\node.exe")
                    // ->setNpmBinary("C:\\Users\\<USER>\\AppData\\Roaming\\npm")
                    ->storeAs($model_name, $upload_file_name);

                $path = $model_name . $upload_file_name;

                File::create([
                    'filename' => $path,
                    'url' => Storage::disk('s3')->url($path),
                    'user_id' => $client->company->users->first()->id,
                    'size' => Storage::disk('s3')->size($path),
                    'company_id' => $client->company->id,
                ]);

                $health_questionary = $client->health_questionaries->first();
                $health_questionary->data_new = null;
                $health_questionary->pdf = $model_name . $upload_file_name;
                $health_questionary->save();
            }
        }
    }

    /**
     * @return string
     */
    protected function generateFileName($path)
    {
        $filename = Str::random(20);

        // Make sure the filename does not exist, if it does, just regenerate
        while (Storage::disk('s3')->exists($path . $filename . '.pdf')) {
            $filename = Str::random(20);
        }

        return $filename;
    }
}
