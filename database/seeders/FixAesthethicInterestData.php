<?php

namespace Database\Seeders;

use App\AestheticInterest;
use Illuminate\Database\Seeder;

class FixAesthethicInterestData extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // $clients = AestheticInterest::where("data_new",'!=',null)->select('data_new','id')->get()->toArray();
        $aesthethic_interests = AestheticInterest::where("data_new",'!=',null)->get();

        foreach ($aesthethic_interests as $index => $aesthethic_interest) {
            $data_new = $aesthethic_interest->data_new;
            if(is_array($data_new)){
                if(isset($data_new['aesthetic_interest'][7]['image'])){
                    unset($data_new['aesthetic_interest'][7]['image']);
                }
            }
            $aesthethic_interest->data_new = json_encode($data_new);
            $aesthethic_interest->save();
        }
    }
}
