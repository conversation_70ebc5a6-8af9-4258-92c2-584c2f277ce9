<?php

namespace Database\Seeders;


use App\Company;
use Illuminate\Database\Seeder;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;

class CompressUserAndCompanyProfileImage extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companies = Company::with('file', 'users.file')
            ->cursor();

        foreach ($companies as $index => $company) {
            if ($company->profile_photo && Storage::disk('s3')->exists($company->profile_photo)) {
                try {
                    print_r($index . "\n");
                    $ext = pathinfo(basename($company->profile_photo), PATHINFO_EXTENSION);

                    Storage::disk('public')->put("test.$ext", Storage::disk('s3')->get($company->profile_photo));

                    $image = Image::make(Storage::disk('public')->path("test.$ext"))->resize(500, 500, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    })->orientate();

                    Storage::disk('s3')->put($company->profile_photo, $image->stream()->__toString());
                } catch (\Throwable $th) {
                    throw $th;
                }
            }

            foreach ($company->users as $index => $user) {
                try {
                    if ($user->profile_photo && Storage::disk('s3')->exists($user->profile_photo)) {

                        print_r($index . "\n");
                        $ext = pathinfo(basename($user->profile_photo), PATHINFO_EXTENSION);

                        Storage::disk('public')->put("test.$ext", Storage::disk('s3')->get($user->profile_photo));

                        $image = Image::make(Storage::disk('public')->path("test.$ext"))->resize(500, 500, function ($constraint) {
                            $constraint->aspectRatio();
                            $constraint->upsize();
                        })->orientate();

                        Storage::disk('s3')->put($user->profile_photo, $image->stream()->__toString());
                    }
                } catch (\Throwable $th) {
                    throw $th;
                }
            }
        }
    }
}
