<?php

namespace Database\Seeders;

use App\Company;
use Illuminate\Database\Seeder;

class CompleteFreeTrailOfRecordSubscription extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companies = Company::where('is_free_trail_finished', false)
            ->where(function ($query) {
                $query->where('free_trail_end_date', null)->orWhere('free_trail_end_date', '<', now()->format('Y-m-d'));
            })
            ->whereHas('subscriptions', function ($query) {
                $query->active();
            })
            ->cursor();

        foreach ($companies as $key => $company) {
            try {
                $company->is_free_trail_finished = true;
                $company->save();
            } catch (\Throwable $th) {
            }
        }
    }
}
