<?php

namespace Database\Seeders;


use App\Plan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;

class PlanTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Plan::truncate();

        if (App::environment('testing') || App::environment('local')) {
            //FREE PLANS
            Plan::create([
                'name' => 'Free',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MABDvCBmBWVCuBd5BmaHyPP',
                'cost' => 'Free',
                'description' => 'This is free version.',
                'users' => '1',
                'client' => '21',
                'storage' => '1',
                'cost_value' => 0,
                'is_free' => true,
                'currency' => "€",
                'is_2022' => 1,
                'is_yearly' => 0,
            ]);

            Plan::create([
                'name' => 'Free',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MABChCBmBWVCuBdoAbcElZe',
                'cost' => 'Free',
                'description' => 'This is free version.',
                'users' => '1',
                'client' => '21',
                'storage' => '1',
                'cost_value' => 0,
                'is_free' => true,
                'currency' => "£",
                'is_2022' => 1,
                'is_yearly' => 0,
            ]);

            Plan::create([
                'name' => 'Free',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MABDGCBmBWVCuBddFg8KfZf',
                'cost' => 'Free',
                'description' => 'This is free version.',
                'users' => '1',
                'client' => '21',
                'storage' => '1',
                'cost_value' => 0,
                'is_free' => true,
                'currency' => "$",
                'is_2022' => 1,
                'is_yearly' => 0,
            ]);

            Plan::create([
                'name' => 'Free',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MAB4sCBmBWVCuBdM02k4pRx',
                'cost' => 'Free',
                'description' => 'This is free version.',
                'users' => '1',
                'client' => '21',
                'storage' => '1',
                'cost_value' => 0,
                'is_free' => true,
                'currency' => "kr",
                'is_2022' => 1,
                'is_yearly' => 0,
            ]);

            //PAID-MONTHLY PLANS
            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MATQGCBmBWVCuBdwd5iNYR4',
                'cost' => '24€ / per user, month',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => 24,
                'is_free' => false,
                'currency' => "€",
                'is_2022' => 1,
                'is_yearly' => 0,
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MABIACBmBWVCuBdj2sQuNXZ',
                'cost' => '26$ / per user, month',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => 26,
                'is_free' => false,
                'currency' => "$",
                'is_2022' => 1,
                'is_yearly' => 0,
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MABLmCBmBWVCuBd5ZciHYor',
                'cost' => '22£ / per user, month',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => 22,
                'is_free' => false,
                'currency' => "£",
                'is_2022' => 1,
                'is_yearly' => 0,
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MABKoCBmBWVCuBdETKp49rW',
                'cost' => '249kr / per user, month',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => 249,
                'is_free' => false,
                'currency' => "kr",
                'is_2022' => 1,
                'is_yearly' => 0,
            ]);

            //PAID-YEARLY PLANS
            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MtmcvCBmBWVCuBdiBiUtfhv',
                'cost' => '230€ / per user, year',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => 230,
                'is_free' => false,
                'currency' => "€",
                'is_2022' => 1,
                'is_yearly' => 1,
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1Mtma4CBmBWVCuBdtnwqH9NF',
                'cost' => '250$ / per user, year',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => 250,
                'is_free' => false,
                'currency' => "$",
                'is_2022' => 1,
                'is_yearly' => 1,
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MtmYZCBmBWVCuBdVY7mX1LR',
                'cost' => '211£ / per user, year',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => 211,
                'is_free' => false,
                'currency' => "£",
                'is_2022' => 1,
                'is_yearly' => 1,
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MtmW2CBmBWVCuBd6TMu6VjN',
                'cost' => '2390kr / per user, year',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => 2390,
                'is_free' => false,
                'currency' => "kr",
                'is_2022' => 1,
                'is_yearly' => 1,
            ]);



            // Plan::create([
            //     'name' => 'Single',
            //     'subscription_id' => 'Subscription',
            //     'plan_id' => 'plan_H2wUFkCw6GlUAD',
            //     'cost' => '5€ / per month',
            //     'description' => 'This is single plan.',
            //     'users' => '1',
            //     'client' => '500',
            //     'storage' => '5',

            //     'cost_value' => 5,
            //     'is_free' => false,
            //     'currency' => "€",
            // ]);
            // Plan::create([
            //     'name' => 'Medium',
            //     'subscription_id' => 'Subscription',
            //     'plan_id' => 'plan_H2wXqpUDYEPnXP',
            //     'cost' => '25€ / per month',
            //     'description' => 'This is medium plan.',
            //     'users' => '5',
            //     'client' => '5000',
            //     'storage' => '40',

            //     'cost_value' => 25,
            //     'is_free' => false,
            //     'currency' => "€",
            // ]);
            // Plan::create([
            //     'name' => 'Full',
            //     'subscription_id' => 'Subscription',
            //     'plan_id' => 'plan_H2wYT8yzrA5Bqx',
            //     'cost' => '59€ / per month',
            //     'description' => 'This is full plan.',
            //     'users' => '20',
            //     'client' => '10000',
            //     'storage' => '160',

            //     'cost_value' => 59,
            //     'is_free' => false,
            //     'currency' => "€",
            // ]);
        } else {
            Plan::create([
                'name' => 'Free',
                'subscription_id' => 'Subscription',
                'plan_id' => 'plan_HInwLF7p1lgjk2',
                'cost' => 'Free',
                'description' => 'This is free version.',
                'users' => '1',
                'client' => '20',
                'storage' => '1',

                'cost_value' => 0,
                'is_free' => true,
                'currency' => "€",
            ]);

            Plan::create([
                'name' => 'Free',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1It9oDCBmBWVCuBdxioStgMG',
                'cost' => 'Free',
                'description' => 'This is free version.',
                'users' => '1',
                'client' => '20',
                'storage' => '1',

                'cost_value' => 0,
                'is_free' => true,
                'currency' => "£",
            ]);

            Plan::create([
                'name' => 'Free',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1It9nLCBmBWVCuBdx3tuZ4ka',
                'cost' => 'Free',
                'description' => 'This is free version.',
                'users' => '1',
                'client' => '20',
                'storage' => '1',

                'cost_value' => 0,
                'is_free' => true,
                'currency' => "$",
            ]);

            Plan::create([
                'name' => 'Free',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1It9msCBmBWVCuBdlfNmXtgF',
                'cost' => 'Free',
                'description' => 'This is free version.',
                'users' => '1',
                'client' => '20',
                'storage' => '1',

                'cost_value' => 0,
                'is_free' => true,
                'currency' => "kr",
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1IlYRUCBmBWVCuBdBsmscuFH',
                'cost' => '14€ / per user, month',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '500',
                'storage' => '1',

                'cost_value' => 14,
                'is_free' => false,
                'currency' => "€",
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1It8Z0CBmBWVCuBdRPatw13P',
                'cost' => '17$ / per user, month',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '500',
                'storage' => '1',

                'cost_value' => 17,
                'is_free' => false,
                'currency' => "$",
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1It8ZfCBmBWVCuBdJq1WThzh',
                'cost' => '12£ / per user, month',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '500',
                'storage' => '1',

                'cost_value' => 12,
                'is_free' => false,
                'currency' => "£",
            ]);

            Plan::create([
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1It8aFCBmBWVCuBdpS0WqGFe',
                'cost' => '140kr / per user, month',
                'description' => 'This is licensed plan.',
                'users' => '1',
                'client' => '500',
                'storage' => '1',

                'cost_value' => 140,
                'is_free' => false,
                'currency' => "kr",
            ]);

            Plan::create([
                'name' => 'Single',
                'subscription_id' => 'Subscription',
                'plan_id' => 'plan_HInwPAWQvXHRBc',
                'cost' => '5€ / per month',
                'description' => 'This is single plan.',
                'users' => '1',
                'client' => '500',
                'storage' => '5',

                'cost_value' => 5,
                'is_free' => false,
                'currency' => "€",
            ]);
            Plan::create([
                'name' => 'Medium',
                'subscription_id' => 'Subscription',
                'plan_id' => 'plan_HInwnnf5eqtMLB',
                'cost' => '25€ / per month',
                'description' => 'This is medium plan.',
                'users' => '5',
                'client' => '5000',
                'storage' => '40',

                'cost_value' => 25,
                'is_free' => false,
                'currency' => "€",
            ]);
            Plan::create([
                'name' => 'Full',
                'subscription_id' => 'Subscription',
                'plan_id' => 'plan_HInwfnuKz8QiE2',
                'cost' => '59€ / per month',
                'description' => 'This is full plan.',
                'users' => '20',
                'client' => '10000',
                'storage' => '160',

                'cost_value' => 20,
                'is_free' => false,
                'currency' => "€",
            ]);
        }
    }
}