<?php

namespace Database\Seeders;


use App\Client;
use Illuminate\Database\Seeder;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;

class CompressProfileImage extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $clients = Client::has('file')
            ->with('file')
            ->without('addresses', 'letter_of_consents', 'general_notes', 'treatments', 'health_questionaries', 'general_questions', 'aesthetic_insterests', 'covid19s')
            ->cursor();

        foreach ($clients as $index => $client) {
            try {
                if (Storage::disk('s3')->exists($client->file->filename)) {
                    print_r($index."\n");
                    $ext = pathinfo(basename($client->file->filename), PATHINFO_EXTENSION);

                    Storage::disk('public')->put("test.$ext", Storage::disk('s3')->get($client->file->filename));

                    $image = Image::make(Storage::disk('public')->path("test.$ext"))->resize(500, 500, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    })->orientate();

                    Storage::disk('s3')->put($client->file->filename, $image->stream()->__toString());

                    $file = $client->file;
                    $file->size = Storage::disk('s3')->size($client->file->filename);
                    $file->save();
                }
            } catch (\Throwable $th) {
                throw $th;
            }
        }
    }
}
