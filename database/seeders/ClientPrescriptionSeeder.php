<?php

namespace Database\Seeders;

use App\Client;
use App\ClientPrescription;
use App\User;
use Illuminate\Database\Seeder;

class ClientPrescriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $clients = Client::get();
        // $users = User::get();
        foreach ($clients as $client) {
            $user = User::inRandomOrder()->first();
            ClientPrescription::factory(3)
                ->for($client, 'client')
                ->for($user, 'assigned_to')
                ->create();
        }
    }
}
