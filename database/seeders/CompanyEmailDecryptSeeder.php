<?php

namespace Database\Seeders;

use App\Company;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Crypt;

class CompanyEmailDecryptSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companies = Company::cursor();

        foreach ($companies as $key => $company) {
            try {
                $company->email = Crypt::decrypt($company->email);
                $company->save();
            } catch (\Throwable $th) {
            }
        }
    }
}
