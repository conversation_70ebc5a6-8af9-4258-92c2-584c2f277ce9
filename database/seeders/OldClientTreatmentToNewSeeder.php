<?php

namespace Database\Seeders;


use App\ClientTreatment;
use App\ClientTreatmentDetail;
use App\Treatment;
use Illuminate\Database\Seeder;

class OldClientTreatmentToNewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $clientTreatments = ClientTreatment::where('treatment_id', "!=", null)->cursor();
        $treatments = Treatment::get();

        foreach ($clientTreatments as $index => $clientTreatment) {
            $treatment = $treatments->where('id', $clientTreatment->treatment_id)->first();
            if (!$treatment) {
                $treatment = $treatments->first();
            }

            ClientTreatmentDetail::updateOrCreate([
                'client_treatment_id' => $clientTreatment->id,
                'treatment_id' => $treatment->id,
            ], [
                'name' => $clientTreatment->name ?? $treatment->name ?? '',
                'description' => $clientTreatment->description ?? $treatment->description ?? '',
                'cost' => $clientTreatment->cost ?? $treatment->cost ?? '',
                'unit' => $clientTreatment->unit ?? $treatment->unit ?? '',
                'color' => $clientTreatment->color ?? $treatment->color ?? '',
                'notes' => $clientTreatment->notes ?? $treatment->notes ?? '',
                'actual_cost' => $clientTreatment->treatment_cost ?? $treatment->cost ?? '',
                'actual_unit' => $clientTreatment->unit ?? $treatment->unit ?? '',
            ]);
        }
    }
}
