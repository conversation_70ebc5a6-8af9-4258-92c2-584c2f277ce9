<?php

namespace Database\Seeders;

use App\User;
use App\Company;
use Illuminate\Database\Seeder;

class CompanyUserTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $company = Company::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'company_name' => 'Meridiq',
            'first_name' => 'Master',
            'last_name' => 'Admin',
            'profile_photo' => '',
            'mobile_number' => '1298287371',
            'street_address' => 'street_address',
            'zip_code' => 'zip_code',
            'city' => 'city',
            'state' => 'state',
            'country' => 'country',
            'unit' => 'usd',
            'password' => bcrypt('password'),
            'language_id' => '1'
        ]);

        $company->users()->firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'title' => 'Meridiq',
            'first_name' => 'Master',
            'last_name' => 'Admin',
            'user_role' => User::MASTER_ADMIN,
            'profile_photo' => '',
            'mobile_number' => '1298287371',
            'street_address' => 'street_address',
            'zip_code' => 'zip_code',
            'city' => 'city',
            'state' => 'state',
            'country' => 'country',
            'password' => bcrypt('password')
        ]);
    }
}
