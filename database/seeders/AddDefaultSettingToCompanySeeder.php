<?php

namespace Database\Seeders;

use App\Company;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AddDefaultSettingToCompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companies = Company::whereDoesntHave('settings', function($query) {
            $query->where('key', 'PORTAL_VIEW_OCCUPATION')
                ->orWhere('key', 'PORTAL_REQUIRED_OCCUPATION')
                ->orWhere('key', 'PORTAL_VIEW_DATE_OF_BIRTH')
                ->orWhere('key', 'PORTAL_REQUIRED_DATE_OF_BIRTH')
                ->orWhere('key', 'PORTAL_VIEW_CITY')
                ->orWhere('key', 'PORTAL_REQUIRED_CITY')
                ->orWhere('key', 'PORTAL_VIEW_PHONE')
                ->orWhere('key', 'PORTAL_REQUIRED_PHONE')
                ->orWhere('key', 'PORTAL_VIEW_STREET_ADDRESS')
                ->orWhere('key', 'PORTAL_REQUIRED_STREET_ADDRESS')
                ->orWhere('key', 'PORTAL_VIEW_ZIPCODE')
                ->orWhere('key', 'PORTAL_REQUIRED_ZIPCODE')
                ->orWhere('key', 'PORTAL_VIEW_STATE')
                ->orWhere('key', 'PORTAL_REQUIRED_STATE')
                ->orWhere('key', 'PORTAL_VIEW_COUNTRY')
                ->orWhere('key', 'PORTAL_REQUIRED_COUNTRY')
                ->orWhere('key', 'PORTAL_VIEW_PROFILE')
                ->orWhere('key', 'PORTAL_REQUIRED_PROFILE');
        })->cursor();

        foreach ($companies as $company) {
            // DB::transaction(function() use($company) {
                
                $company->settings()->updateOrCreate([
                    'key' => 'PORTAL_VIEW_DATE_OF_BIRTH'
                ],[
                    'value' => true,
                ]);

                $company->settings()->updateOrCreate([
                    'key' => 'PORTAL_VIEW_CITY'
                ],[
                    'value' => true,
                ]);

                $company->settings()->updateOrCreate([
                    'key' => 'PORTAL_VIEW_PHONE'
                ],[
                    'value' => true,
                ]);

                $company->settings()->updateOrCreate([
                    'key' => 'PORTAL_VIEW_STREET_ADDRESS'
                ],[
                    'value' => true,
                ]);

                $company->settings()->updateOrCreate([
                    'key' => 'PORTAL_VIEW_PROFILE'
                ],[
                    'value' => true,
                ]);
            // });
        }
    }
}
