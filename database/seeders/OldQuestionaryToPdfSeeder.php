<?php

namespace Database\Seeders;


use App\Covid19;
use App\QuestionaryData;
use App\Traits\SaveFile;
use App\AestheticInterest;
use App\HealthQuestionary;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OldQuestionaryToPdfSeeder extends Seeder
{
    use SaveFile;
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::transaction(function () {

            $health_questionaries = HealthQuestionary::where('data_new', '!=', null)->orWhere('pdf', '!=', null)->has('client')->with('client.company.users')->cursor();

            foreach ($health_questionaries as $index => $health_questionary) {
                if ($health_questionary->pdf) {
                    $questionaryData = QuestionaryData::create([
                        'client_id' => $health_questionary->client_id,
                        'modelable_type' => HealthQuestionary::class,
                        'modelable_id' => $health_questionary->id,
                        'pdf' => $health_questionary->pdf,
                        'response' => '',
                        'created_at' => $health_questionary->updated_at,
                    ]);
                }
                if ($health_questionary->data_new) {
                    $data = ['datas' => json_decode(json_encode($health_questionary->data_new), true)];
                    $file = $this->generateStoreQuestionary($data, 'exports.client_health_questionary', 'clients/' . md5($health_questionary->client_id) . '/health_questionary/pdf', $health_questionary->client->company->users->first());

                    $questionaryData = QuestionaryData::create([
                        'client_id' => $health_questionary->client_id,
                        'modelable_type' => HealthQuestionary::class,
                        'modelable_id' => $health_questionary->id,
                        'pdf' => $file->filename,
                        'response' => collect($data)->values(),
                        'created_at' => $health_questionary->updated_at,
                    ]);
                }
            }

            $aesthethic_interests = AestheticInterest::where('data_new', '!=', null)->orWhere('pdf', '!=', null)->has('client')->with('client.company.users')->cursor();
            foreach ($aesthethic_interests as $index => $aesthethic_interest) {

                if ($aesthethic_interest->pdf) {
                    $questionaryData = QuestionaryData::create([
                        'client_id' => $aesthethic_interest->client_id,
                        'modelable_type' => AestheticInterest::class,
                        'modelable_id' => $aesthethic_interest->id,
                        'pdf' => $aesthethic_interest->pdf,
                        'response' => '',
                        'created_at' => $aesthethic_interest->updated_at,
                    ]);
                }
                if ($aesthethic_interest->data_new && $aesthethic_interest->data_new['aesthetic_interest']) {

                    $data = ['datas' => json_decode(json_encode($aesthethic_interest->data_new['aesthetic_interest']), true)];
                    $file = $this->generateStoreQuestionary($data, 'exports.client_aesthethic_interest_questionary', 'clients/' . md5($aesthethic_interest->client_id) . '/aesthetic_interest/pdf', $aesthethic_interest->client->company->users->first());
                    $questionaryData = QuestionaryData::create([
                        'client_id' => $aesthethic_interest->client_id,
                        'modelable_type' => AestheticInterest::class,
                        'modelable_id' => $aesthethic_interest->id,
                        'pdf' => $file->filename,
                        'response' => collect($data)->values(),
                        'created_at' => $aesthethic_interest->updated_at,
                    ]);
                }
            }

            $covid19s = Covid19::where('data', '!=', null)->has('client')->with('client.company.users')->cursor();
            foreach ($covid19s as $index => $covid19) {
                $data = ['datas' => json_decode(json_encode($covid19->data), true)];
                $file = $this->generateStoreQuestionary($data, 'exports.client_covid19_questionary', 'clients/' . md5($covid19->client_id) . '/covid19_questionary/pdf', $covid19->client->company->users->first());

                $questionaryData = QuestionaryData::create([
                    'client_id' => $covid19->client_id,
                    'modelable_type' => Covid19::class,
                    'modelable_id' => $covid19->id,
                    'pdf' => $file->filename,
                    'response' => collect($data)->values(),
                    'created_at' => $covid19->updated_at,
                ]);
            }
        });
    }
}
