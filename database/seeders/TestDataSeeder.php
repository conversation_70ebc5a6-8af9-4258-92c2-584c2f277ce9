<?php

namespace Database\Seeders;

use App\Company;
use App\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $tester_name = [
            [
                "<PERSON><PERSON>",
                "<PERSON>",
                "<EMAIL>"
            ],
            [
                "<PERSON><PERSON>",
                "<PERSON><PERSON><PERSON>",
                "<EMAIL>"
            ],
            [
                "<PERSON>shan<PERSON>",
                "<PERSON><PERSON>",
                "<EMAIL>"
            ],
            [
                "shreyal",
                "shah",
                "<EMAIL>"
            ],
            [
                "yash",
                "chauhan",
                "<EMAIL>"
            ],
            [
                "darpan",
                "patel",
                "<EMAIL>"
            ],
            [
                "jay",
                "patel",
                "<EMAIL>"
            ],
        ];


        foreach ($tester_name as $name) {
            $company = Company::firstOrCreate([
                'company_name' => $name[0] . "'s company",
                'first_name' => $name[0],
                'last_name' => $name[1],
                'email' => $name[2],
                'mobile_number' => "1298287371",
                'street_address' => "some address",
                'zip_code' => "390019",
                'city' => "vadodara",
                'state' => 'Gujarat',
                'country' => 'India',
                'password' => bcrypt('Secret@123'),
                'country_code' => '91',
                'unit' => 'usd',
                'is_booking_on' => 1,
                'is_record_on' => 1,
                'timezone' => "Asia/Calcutta",
                'language_id' => '1',
                'free_trail_start_date' => Carbon::now(),
                'free_trail_end_date' => Carbon::now()->addDays(15),
                'is_free_trail_finished' => 0,
            ]);
            $company->users()->firstOrCreate([
                'title' => $company->company_name,
                'first_name' => $company->first_name,
                'last_name' => $company->last_name,
                'user_role' => User::ADMIN,
                'is_active' => 1,
                'email' => $company->email,
                'mobile_number' => $company->mobile_number,
                'street_address' => $company->street_address,
                'zip_code' => $company->zip_code,
                'city' => $company->city,
                'state' => $company->state,
                'country' => $company->country,
                'password' => bcrypt('Secret@123'),
                'is_for_booking' => $company->is_booking_on,
                'is_booking_on' => $company->is_booking_on,
                'is_record_on' => $company->is_record_on,
            ]);
        }
    }
}
