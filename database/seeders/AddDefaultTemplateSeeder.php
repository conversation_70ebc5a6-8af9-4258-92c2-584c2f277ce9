<?php

namespace Database\Seeders;

use App\Company;
use App\Template;
use Carbon\Carbon;
use Illuminate\Http\File;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;

class AddDefaultTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (App::environment('testing')) {
            $model_name = "testing/template";
        } else {
            $model_name = "template";
        }

        $human0 = Storage::disk('s3')->putFileAs($model_name, new File(public_path("images/templates/human0.png")), 'human0.png');
        $human1 = Storage::disk('s3')->putFileAs($model_name, new File(public_path("images/templates/human1.png")), 'human1.png');
        $human2 = Storage::disk('s3')->putFileAs($model_name, new File(public_path("images/templates/human2.png")), 'human2.png');
        $human3 = Storage::disk('s3')->putFileAs($model_name, new File(public_path("images/templates/human3.png")), 'human3.png');
        $human4 = Storage::disk('s3')->putFileAs($model_name, new File(public_path("images/templates/human4.png")), 'human4.png');
        $human5 = Storage::disk('s3')->putFileAs($model_name, new File(public_path("images/templates/human5.png")), 'human5.png');

        $companies = Company::cursor();
        foreach ($companies as $index => $company) {
            Template::updateOrCreate([
                'company_id' => $company->id,
                'is_editable' => false,
                'created_at' => Carbon::parse("2010-01-01")->addDay(0)->format('Y-m-d H:i:s'),
            ],[
                'image' => $human0,
                'name' => "Man body back",
            ]);
            
            Template::updateOrCreate([
                'company_id' => $company->id,
                'is_editable' => false,
                'created_at' => Carbon::parse("2010-01-01")->addDay(1)->format('Y-m-d H:i:s'),
            ],[
                'image' => $human1,
                'name' => "Man face",
            ]);

            Template::updateOrCreate([
                'is_editable' => false,
                'company_id' => $company->id,
                'created_at' => Carbon::parse("2010-01-01")->addDay(2)->format('Y-m-d H:i:s'),
            ],[
                'image' => $human2,
                'name' => "Man body front",
            ]);

            Template::updateOrCreate([
                'company_id' => $company->id,
                'is_editable' => false,
                'created_at' => Carbon::parse("2010-01-01")->addDay(3)->format('Y-m-d H:i:s'),
            ],[
                'image' => $human3,
                'name' => "Woman body back",
            ]);

            Template::updateOrCreate([
                'company_id' => $company->id,
                'is_editable' => false,
                'created_at' => Carbon::parse("2010-01-01")->addDay(4)->format('Y-m-d H:i:s'),
            ],[
                'image' => $human4,
                'name' => "Woman face",
            ]);

            Template::updateOrCreate([
                'company_id' => $company->id,
                'is_editable' => false,
                'created_at' => Carbon::parse("2010-01-01")->addDay(5)->format('Y-m-d H:i:s'),
            ],[
                'image' => $human5,
                'name' => "Woman body front",
            ]);
        }
    }
}
