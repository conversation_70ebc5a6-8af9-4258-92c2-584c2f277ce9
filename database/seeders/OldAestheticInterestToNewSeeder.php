<?php

namespace Database\Seeders;

use App\File;
use App\Client;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use VerumConsilium\Browsershot\Facades\PDF;

class OldAestheticInterestToNewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $clients = Client::has('aesthetic_insterests')
            ->whereHas('aesthetic_insterests', function ($query) {
                $query->where('pdf', null);
            })
            ->with(['aesthetic_insterests', 'company', 'company.users'])->get();

        foreach ($clients as $index => $client) {
            $model_name = 'client_aesthetic_insterests';

            if (App::environment('testing')) {
                $model_name = "testing/" . Str::camel($client->company->company_name) . "_" . $client->company->id . '/' . $model_name . '/';
            } else {
                $model_name = Str::camel($client->company->company_name) . "_" . $client->company->id . '/' . $model_name . '/';
            }

            $aesthethic_interest = $client->aesthetic_insterests->first();

            if ($aesthethic_interest &&  $aesthethic_interest->data) {
                $upload_file_name = $this->generateFileName($model_name) . '.pdf';

                PDF::loadView('exports.client_old_aesthethic_interest', ['client' => $client])
                    ->waitUntilNetworkIdle()
                    ->format('A4')
                    ->margins(15, 0, 15, 0)
                    // ->setNodeBinary("C:\\PROGRA~1\\nodejs\\node.exe")
                    // ->setNpmBinary("C:\\Users\\<USER>\\AppData\\Roaming\\npm")
                    ->storeAs($model_name, $upload_file_name);

                $path = $model_name . $upload_file_name;

                File::create([
                    'filename' => $path,
                    'url' => Storage::disk('s3')->url($path),
                    'user_id' => $client->company->users->first()->id,
                    'size' => Storage::disk('s3')->size($path),
                    'company_id' => $client->company->id,
                ]);

                $aesthethic_interest = $client->aesthetic_insterests->first();
                $aesthethic_interest->pdf = $model_name . $upload_file_name;
                $aesthethic_interest->save();
            }
        }
    }


    /**
     * @return string
     */
    protected function generateFileName($path)
    {
        $filename = Str::random(20);

        // Make sure the filename does not exist, if it does, just regenerate
        while (Storage::disk('s3')->exists($path . $filename . '.pdf')) {
            $filename = Str::random(20);
        }

        return $filename;
    }
}
