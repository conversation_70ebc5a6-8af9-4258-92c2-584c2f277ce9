 <?php

namespace Database\Seeders;

use App\ClientTreatment;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class FixClientTreatmentImages extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $client_treatments = ClientTreatment::with('files')->where('updated_at', '>=', Carbon::now()->subDays(3)->format('Y-m-d H:i:s'))->get();

        foreach ($client_treatments as $index => $client_treatment) {
            $client_treatment_images = $client_treatment->files->map(function ($file) {
                return $file->filename;
            });

            $client_treatment->images = json_encode($client_treatment_images);

            $client_treatment->save();
        }
    }
}
