<?php

namespace Database\Seeders;

use App\Company;
use Illuminate\Database\Seeder;
use App\Traits\CreateDefaultCustomHealthQuestionary;

class DefaultCustomQuestionarySeeder extends Seeder
{
    use CreateDefaultCustomHealthQuestionary;
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companies = Company::all();

        foreach ($companies as $key => $company) {
            $this->createDefaultCustomHealthQuestionary($company);
        }
    }
}
