<?php

namespace Database\Seeders;

use App\File;
use Illuminate\Database\Seeder;

class AddMediaThumbnailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $files = File::where('fileable_type', 'App\\ClientMedia')
            ->where('thumbnail', null)
            ->cursor();
        
        foreach ($files as $file) {
            $file->thumbnail = $file->generateThumbnail();
            $file->save();
        }
    }
}
