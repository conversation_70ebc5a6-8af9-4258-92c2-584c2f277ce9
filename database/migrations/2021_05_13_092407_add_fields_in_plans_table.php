<?php

use App\Plan;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFieldsInPlansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Plan::truncate();
        Schema::table('plans', function (Blueprint $table) {
            $table->decimal('cost_value');
            $table->boolean('is_free')->default(false);
            $table->string('currency');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('plans', function (Blueprint $table) {
            $table->dropColumn(['cost_value', 'is_free', 'currency']);
        });
    }
}
