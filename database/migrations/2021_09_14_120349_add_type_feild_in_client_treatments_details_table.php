<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Schema;

class AddTypeFeildInClientTreatmentsDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('client_treatment_details', function (Blueprint $table) {
            $table->longText('description')->nullable()->change();
            $table->longText('cost')->nullable()->change();
            $table->string('type')->default(Crypt::encrypt('treatment'));
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('client_treatment_details', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
}
