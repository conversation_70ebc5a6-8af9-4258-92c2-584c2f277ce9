<?php

namespace Database\Factories;

use App\CompanyTerminal;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyTerminalFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CompanyTerminal::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            //
        ];
    }
}
