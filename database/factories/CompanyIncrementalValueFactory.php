<?php

namespace Database\Factories;

use App\CompanyIncrementalValue;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyIncrementalValueFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CompanyIncrementalValue::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            //
        ];
    }
}
