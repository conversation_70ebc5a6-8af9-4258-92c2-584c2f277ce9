<?php

namespace Database\Factories;

use App\CompanyReceiptRefund;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyReceiptRefundFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CompanyReceiptRefund::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            //
        ];
    }
}
