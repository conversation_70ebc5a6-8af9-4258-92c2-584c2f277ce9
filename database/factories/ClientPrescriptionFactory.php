<?php

namespace Database\Factories;

use App\ClientPrescription;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClientPrescriptionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ClientPrescription::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'title' => $this->faker->text(100),
            'description' => $this->faker->text(200),
        ];
    }
}
