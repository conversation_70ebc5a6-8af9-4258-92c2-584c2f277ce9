<?php

namespace Database\Factories;

use App\CompanyReceiptRefundItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyReceiptRefundItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CompanyReceiptRefundItem::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            //
        ];
    }
}
