<?php

namespace Database\Factories;

use App\UserNotification;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserNotificationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserNotification::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'title' => $this->faker->text(20),
            'description' => $this->faker->text(50),
            'type' => $this->faker->randomElement([UserNotification::NOTIFICATION,UserNotification::SMS,UserNotification::EMAIL]),
        ];
    }
}